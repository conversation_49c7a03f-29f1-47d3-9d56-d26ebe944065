# 验证码功能使用说明

## 功能概述

验证码功能已完全实现，支持邮件和短信两种发送方式，用于用户注册、登录和密码重置等场景。

## 主要特性

### ✅ 验证码生成和管理
- 生成6位数字验证码
- 5分钟有效期
- 最多3次验证尝试
- 自动清理过期验证码

### ✅ 发送方式
- **邮件验证码**: 支持SMTP服务器（Gmail、QQ邮箱等）
- **短信验证码**: 支持阿里云、腾讯云、华为云等服务商

### ✅ 安全机制
- 发送频率限制（1分钟内不能重复发送）
- 验证次数限制（最多3次错误尝试）
- 验证码过期自动清理
- 验证成功后立即删除

## API 接口

### 1. 发送验证码
```
POST /api/v1/auth/send-verification-code
Content-Type: application/json

{
  "identifier": "<EMAIL>",  // 邮箱或手机号
  "type": "register"                 // register | reset_password | login
}
```

### 2. 重置密码（使用验证码）
```
POST /api/v1/auth/reset-password
Content-Type: application/json

{
  "identifier": "<EMAIL>",
  "verificationCode": "123456",
  "newPassword": "newpassword123"
}
```

## 配置说明

### 邮件服务配置
```env
# .env 文件
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### 短信服务配置
```env
# .env 文件
SMS_PROVIDER=aliyun     # aliyun | tencent | huawei
SMS_API_KEY=your-api-key
SMS_API_SECRET=your-api-secret
```

## 开发和测试

### 测试模式
- 未配置SMTP时，验证码会输出到控制台
- 开发环境下，短信验证码会输出到日志
- 所有验证码功能都有完整的单元测试

### 运行测试
```bash
npm test -- test/verification-code.spec.ts
```

## 生产环境部署

1. **配置邮件服务**
   - 获取SMTP服务器信息
   - 设置应用专用密码（推荐）
   - 更新.env文件

2. **配置短信服务**
   - 选择短信服务商（阿里云、腾讯云等）
   - 获取API密钥
   - 实现对应的发送逻辑

3. **安全建议**
   - 使用环境变量存储敏感信息
   - 定期轮换API密钥
   - 监控发送量和费用

## 错误处理

- `验证码发送太频繁，请稍后再试`: 1分钟内重复发送
- `验证码无效或已过期`: 验证码错误或超过5分钟
- `手机号格式不正确`: 手机号格式验证失败
- `邮件发送失败`: SMTP配置错误或网络问题

## 扩展功能

可以根据需要扩展以下功能：
- 图形验证码（防机器人）
- 验证码发送记录和统计
- 不同场景的验证码模板
- 多语言支持
- 语音验证码