# HealthyLife Backend API

基于NestJS + MongoDB的智能健康管理应用后端服务

## 🚀 技术栈

- **框架**: NestJS 10.x
- **数据库**: MongoDB
- **ODM**: Mongoose
- **认证**: JWT + Passport
- **文档**: Swagger/OpenAPI
- **语言**: TypeScript

## 📁 项目结构

```
HealthyLife-Backend/
├── src/
│   ├── auth/                 # 认证模块
│   │   ├── guards/          # 守卫
│   │   ├── strategies/      # 认证策略
│   │   └── dto/            # 数据传输对象
│   ├── users/               # 用户管理模块
│   ├── body-data/           # 体脂数据模块
│   ├── devices/             # 设备管理模块
│   ├── suggestions/         # AI建议模块
│   ├── schemas/             # MongoDB数据模型
│   ├── common/              # 公共模块
│   ├── config/              # 配置模块
│   └── database/            # 数据库模块
├── .env                     # 环境变量
├── package.json
└── README.md
```

## 🛠 主要功能

### 1. 用户认证系统
- JWT令牌认证
- 多种登录方式支持（邮箱、Google、Apple、手机）
- 密码加密存储
- 权限管理

### 2. 用户管理
- 用户信息CRUD
- 个人资料管理
- 用户设置和偏好
- 用户统计数据

### 3. 体脂数据管理
- 体脂数据CRUD操作
- 数据验证和质量评分
- 数据统计和分析
- 历史数据查询

### 4. 设备管理
- 蓝牙设备注册和管理
- 设备状态监控
- 自动同步配置
- 设备统计信息

### 5. AI建议系统
- 基于ChatGPT的智能建议生成
- 建议分类和优先级
- 用户反馈收集
- 个性化推荐

## 📊 数据模型

### User (用户)
```typescript
{
  _id: ObjectId,
  email: string,
  name: string,
  passwordHash?: string,
  avatar?: string,
  authProvider: 'email' | 'google' | 'apple' | 'phone',
  settings: {
    notifications: {...},
    privacy: {...},
    preferences: {...}
  },
  profile: {
    height?: number,
    gender?: string,
    birthday?: Date,
    activityLevel?: string
  }
}
```

### BodyData (体脂数据)
```typescript
{
  _id: ObjectId,
  userId: ObjectId,
  timestamp: Date,
  weight: number,
  bodyFatPercentage: number,
  muscleMass: number,
  boneMass: number,
  waterPercentage: number,
  visceralFat: number,
  bmr: number,
  bmi: number,
  deviceId: string,
  sourceType: 'bluetooth' | 'manual' | 'api'
}
```

### Suggestion (AI建议)
```typescript
{
  _id: ObjectId,
  userId: ObjectId,
  content: string,
  category: 'diet' | 'exercise' | 'lifestyle' | 'general',
  priority: 'low' | 'medium' | 'high',
  basedOnData: ObjectId[],
  generationType: 'ai' | 'rule' | 'manual',
  aiMeta?: {...}
}
```

### Device (设备)
```typescript
{
  _id: ObjectId,
  userId: ObjectId,
  name: string,
  type: 'body_scale' | 'fitness_tracker' | 'smart_watch' | 'other',
  bluetoothId: string,
  status: 'active' | 'inactive' | 'error',
  config: {...},
  syncStats: {...}
}
```

## 🔧 环境配置

复制 `.env` 文件并配置以下变量：

```bash
# 数据库配置
MONGODB_URI=mongodb://localhost:27017/healthylife
DATABASE_NAME=healthylife

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# 应用配置
PORT=3000
NODE_ENV=development

# OpenAI配置
OPENAI_API_KEY=your-openai-api-key-here

# OAuth配置
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## 🚀 快速开始

### 1. 安装依赖
```bash
cd HealthyLife-Backend
npm install
```

### 2. 启动MongoDB
```bash
# 使用Docker启动MongoDB
docker run -d -p 27017:27017 --name mongodb mongo:latest

# 或使用本地MongoDB服务
mongod
```

### 3. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入实际配置
```

### 4. 启动开发服务器
```bash
npm run start:dev
```

### 5. 访问API文档
打开浏览器访问: http://localhost:3000/api/docs

## 📚 API接口

### 认证接口
- `POST /api/v1/auth/login` - 邮箱登录
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/google` - Google登录
- `POST /api/v1/auth/refresh` - 刷新令牌

### 用户接口
- `GET /api/v1/users/profile` - 获取用户信息
- `PATCH /api/v1/users/profile` - 更新用户信息
- `GET /api/v1/users/profile/stats` - 获取用户统计

### 体脂数据接口
- `GET /api/v1/body-data` - 获取体脂数据列表
- `POST /api/v1/body-data` - 创建体脂数据
- `GET /api/v1/body-data/stats` - 获取数据统计
- `GET /api/v1/body-data/trends` - 获取趋势分析

### 设备管理接口
- `GET /api/v1/devices` - 获取设备列表
- `POST /api/v1/devices` - 注册新设备
- `PATCH /api/v1/devices/:id` - 更新设备信息
- `DELETE /api/v1/devices/:id` - 删除设备

### AI建议接口
- `GET /api/v1/suggestions` - 获取建议列表
- `POST /api/v1/suggestions/generate` - 生成新建议
- `PATCH /api/v1/suggestions/:id/feedback` - 提交反馈

## 🛡 安全特性

- **密码加密**: 使用bcryptjs加密存储密码
- **JWT认证**: 安全的令牌认证机制
- **请求验证**: 使用class-validator验证输入
- **CORS配置**: 跨域请求控制
- **Helmet**: 安全HTTP头设置
- **速率限制**: API请求频率限制

## 📈 性能优化

- **数据库索引**: 优化查询性能
- **数据验证**: 输入数据质量控制
- **缓存策略**: Redis缓存支持
- **压缩**: 响应数据压缩
- **连接池**: MongoDB连接池优化

## 🧪 测试

```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

## 📦 部署

### Docker部署
```bash
# 构建镜像
docker build -t healthylife-api .

# 运行容器
docker run -p 3000:3000 healthylife-api
```

### PM2部署
```bash
# 构建项目
npm run build

# 使用PM2启动
pm2 start dist/main.js --name healthylife-api
```

## 🔍 监控和日志

- **健康检查**: `/api/v1/health`
- **应用信息**: `/api/v1/info`
- **日志系统**: Winston日志记录
- **性能监控**: 内置性能指标

## 🤝 API集成示例

### JavaScript/React Native集成
```javascript
// 登录示例
const response = await fetch('http://localhost:3000/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

const { accessToken } = await response.json();

// 使用令牌访问受保护的接口
const userProfile = await fetch('http://localhost:3000/api/v1/users/profile', {
  headers: {
    'Authorization': `Bearer ${accessToken}`
  }
});
```

## 📝 开发指南

### 添加新的模块
1. 创建模块目录
2. 定义数据模型（Schema）
3. 创建DTO类
4. 实现服务类
5. 创建控制器
6. 注册模块

### 数据库迁移
```bash
# 创建迁移文件
npm run migration:create <migration-name>

# 运行迁移
npm run migration:up
```

## 🐛 常见问题

### Q: MongoDB连接失败？
A: 确保MongoDB服务正在运行，检查连接字符串配置

### Q: JWT令牌验证失败？
A: 检查JWT_SECRET配置，确保前后端使用相同的密钥

### Q: OpenAI API调用失败？
A: 验证API密钥有效性，检查网络连接和配额

## 📄 许可证

MIT License

---

**HealthyLife Backend API** - 为健康管理提供强大的后端支持！ 💪🏥📊