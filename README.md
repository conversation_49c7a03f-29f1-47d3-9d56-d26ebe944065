# HealthyLife - 智能健康管理应用

一个现代化的跨平台智能健康管理应用，支持蓝牙设备连接、AI健康建议和数据可视化。

## 🌟 核心功能

### ✅ 已实现功能

- **🔐 用户认证系统**
  - 邮箱/密码登录注册
  - Google OAuth登录
  - Apple ID登录  
  - 手机号验证码登录

- **📱 现代化UI界面**
  - 精美的Material Design风格界面
  - 响应式设计，支持多种屏幕尺寸
  - 直观的底部标签导航
  - 实时数据展示

- **🔵 蓝牙设备连接**
  - 自动扫描附近的健康设备
  - 支持体脂秤等智能设备
  - 实时数据接收和解析
  - 设备连接状态管理

- **📊 体脂数据管理**
  - 手动数据录入
  - 蓝牙设备自动同步
  - 历史数据查看和管理
  - 数据趋势分析

- **🤖 AI智能建议**
  - 基于ChatGPT的个性化健康建议
  - 根据体脂数据生成针对性建议
  - 分类建议（饮食、运动、生活方式）
  - 智能内容解析

- **📈 数据可视化**
  - 交互式图表展示
  - 多维度数据趋势分析
  - 体重、体脂率、肌肉量等指标
  - 统计数据总览

- **🔔 推送通知**
  - 每日健康提醒
  - 测量提醒设置
  - AI建议推送
  - 自定义通知时间

## 🛠 技术栈

- **前端框架**: React Native + Expo
- **状态管理**: Redux Toolkit
- **导航**: React Navigation 6
- **UI组件**: React Native Elements + Custom Components
- **图表库**: React Native Chart Kit
- **蓝牙**: React Native BLE PLX
- **通知**: Expo Notifications
- **认证**: Expo Auth Session
- **语言**: TypeScript
- **样式**: StyleSheet (类似CSS-in-JS)

## 📦 项目结构

```
HealthyLife/
├── src/
│   ├── components/          # 可复用组件
│   │   ├── BodyDataChart.tsx
│   │   ├── BodyDataInputModal.tsx
│   │   └── BodyDataList.tsx
│   ├── screens/             # 页面组件
│   │   ├── AuthScreen.tsx
│   │   ├── HomeScreen.tsx
│   │   ├── DataScreen.tsx
│   │   ├── DevicesScreen.tsx
│   │   ├── SuggestionsScreen.tsx
│   │   └── NotificationSettingsScreen.tsx
│   ├── services/            # 业务逻辑服务
│   │   ├── AuthService.ts
│   │   ├── BluetoothService.ts
│   │   ├── BodyDataService.ts
│   │   ├── ChatGPTService.ts
│   │   ├── NotificationService.ts
│   │   └── PermissionService.ts
│   ├── store/               # Redux状态管理
│   │   ├── slices/
│   │   └── index.ts
│   ├── types/               # TypeScript类型定义
│   ├── hooks/               # 自定义Hooks
│   ├── utils/               # 工具函数
│   ├── config/              # 配置文件
│   └── navigation/          # 导航配置
├── assets/                  # 静态资源
├── App.tsx                  # 应用入口
└── package.json
```

## 🚀 快速开始

### 前置要求

- Node.js (v16+)
- npm 或 yarn
- Expo CLI
- React Native开发环境

### 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd HealthyLife
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm start
   ```

4. **在设备上运行**
   - 扫描控制台中的二维码使用Expo Go应用
   - 或按 `a` 在Android模拟器中运行
   - 或按 `i` 在iOS模拟器中运行

### 配置

1. **ChatGPT API配置**
   ```typescript
   // src/config/index.ts
   export const Config = {
     openai: {
       apiKey: 'your-openai-api-key-here', // 替换为实际API密钥
       baseUrl: 'https://api.openai.com/v1',
       model: 'gpt-3.5-turbo',
     },
   };
   ```

2. **后端API配置**
   ```typescript
   // src/config/index.ts
   api: {
     baseUrl: 'https://your-backend-api.com/api', // 替换为实际API地址
     timeout: 10000,
   },
   ```

## 📱 应用使用指南

### 1. 用户注册和登录
- 支持邮箱、Google、Apple、手机号多种登录方式
- 首次使用需要创建账户
- 登录后自动保持会话状态

### 2. 设备连接
- 进入"设备"页面
- 点击"扫描设备"按钮
- 选择要连接的体脂秤设备
- 连接成功后可自动接收数据

### 3. 数据录入
- 手动录入：点击"+"按钮手动输入数据
- 自动同步：连接设备后自动接收数据
- 数据包括：体重、体脂率、肌肉量、BMI等

### 4. 查看分析
- "数据"页面查看详细记录
- 图表展示数据趋势
- 支持多种指标切换
- 查看统计信息

### 5. AI建议
- "建议"页面查看个性化建议
- 点击"生成AI建议"获取新建议
- 建议分为饮食、运动、生活方式等类别

### 6. 通知设置
- 设置每日提醒时间
- 选择测量提醒日期
- 配置AI建议推送

## 🔧 开发指南

### 添加新功能

1. **创建新的屏幕组件**
   ```typescript
   // src/screens/NewScreen.tsx
   import React from 'react';
   import { View, Text } from 'react-native';
   
   const NewScreen: React.FC = () => {
     return (
       <View>
         <Text>新页面</Text>
       </View>
     );
   };
   
   export default NewScreen;
   ```

2. **添加到导航**
   ```typescript
   // src/navigation/AppNavigator.tsx
   import NewScreen from '../screens/NewScreen';
   
   // 在TabNavigator中添加新的Tab
   ```

3. **创建对应的Redux slice**
   ```typescript
   // src/store/slices/newSlice.ts
   import { createSlice } from '@reduxjs/toolkit';
   
   const newSlice = createSlice({
     name: 'new',
     initialState: {},
     reducers: {},
   });
   
   export default newSlice.reducer;
   ```

### 调试技巧

1. **启用调试模式**
   ```typescript
   // src/config/index.ts
   export const DEBUG = {
     bluetooth: true,
     api: true,
     chatgpt: true,
   };
   ```

2. **查看控制台日志**
   - 打开Expo开发者工具
   - 查看Metro Bundler日志
   - 使用React Native Debugger

3. **测试蓝牙功能**
   - 需要在真实设备上测试
   - 确保设备支持蓝牙
   - 检查权限设置

## 📦 打包发布

```bash
# 安装EAS CLI
npm install -g @expo/eas-cli

# 构建Android APK
eas build --platform android --profile preview

# 构建iOS应用
eas build --platform ios --profile preview
```

### Android APK

1. **配置app.json**
   ```json
   {
     "expo": {
       "name": "HealthyLife",
       "slug": "healthylife",
       "version": "1.0.0",
       "android": {
         "package": "com.yourcompany.healthylife",
         "permissions": [
           "BLUETOOTH",
           "BLUETOOTH_ADMIN",
           "ACCESS_FINE_LOCATION"
         ]
       }
     }
   }
   ```

2. **构建APK**
   ```bash
   expo build:android
   ```

### iOS IPA

1. **配置iOS设置**
   ```json
   {
     "expo": {
       "ios": {
         "bundleIdentifier": "com.yourcompany.healthylife",
         "infoPlist": {
           "NSBluetoothAlwaysUsageDescription": "使用蓝牙连接健康设备",
           "NSBluetoothPeripheralUsageDescription": "使用蓝牙连接健康设备"
         }
       }
     }
   }
   ```

2. **构建IPA**
   ```bash
   expo build:ios
   ```

## 🔐 权限说明

应用需要以下权限：

- **蓝牙权限**: 连接和通信智能设备
- **位置权限**: Android设备扫描蓝牙需要
- **通知权限**: 发送健康提醒和建议
- **网络权限**: API调用和数据同步

## 🐛 常见问题

### Q: 蓝牙设备无法连接？
A: 
1. 确保设备已开启蓝牙
2. 检查应用权限设置
3. 重启蓝牙和应用
4. 确保设备处于配对模式

### Q: AI建议无法生成？
A: 
1. 检查网络连接
2. 验证OpenAI API密钥
3. 确保有足够的API额度
4. 查看控制台错误信息

### Q: 通知不显示？
A: 
1. 检查系统通知权限
2. 确保应用未被禁用通知
3. 检查勿扰模式设置
4. 重新授权通知权限

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送到分支: `git push origin feature/new-feature`
5. 提交Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 📞 联系我们

- 邮箱: <EMAIL>
- GitHub: [HealthyLife Repository](https://github.com/yourcompany/healthylife)

---

**HealthyLife** - 让健康管理更智能、更简单！ 💪🏃‍♀️📊"