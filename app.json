{"expo": {"name": "HealthyLife", "slug": "healthylife", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#6366f1"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.healthylife.app", "buildNumber": "1", "infoPlist": {"NSBluetoothAlwaysUsageDescription": "使用蓝牙连接智能健康设备，如体脂秤，以便自动同步您的健康数据。", "NSBluetoothPeripheralUsageDescription": "使用蓝牙连接智能健康设备，如体脂秤，以便自动同步您的健康数据。", "NSLocationWhenInUseUsageDescription": "在某些Android设备上，扫描蓝牙设备需要位置权限。"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#6366f1"}, "edgeToEdgeEnabled": true, "predictiveBackGestureEnabled": false, "package": "com.healthylife.app", "versionCode": 1, "permissions": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_CONNECT", "android.permission.BLUETOOTH_SCAN", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.VIBRATE", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.WAKE_LOCK"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#6366f1"}]], "scheme": "healthylife", "extra": {"eas": {"projectId": "your-project-id-here"}}}}