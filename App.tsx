import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { Provider, useSelector } from 'react-redux';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { store, persistor } from './src/store';
import AppNavigator from './src/navigation/AppNavigator';
import { RootState } from './src/store';
import { setLanguage as i18nSetLanguage } from './src/i18n';
import { PersistGate } from 'redux-persist/integration/react';
import { useDispatch } from 'react-redux';
import { setToken } from './src/store/slices/authSlice';
import { fetchCurrentUser } from './src/store/slices/authSlice';
import * as secureStore from './src/utils/secureStore';
import { AppDispatch } from './src/store';

const AppWithLanguageSync: React.FC = () => {
  const language = useSelector((state: RootState) => state.auth.language);
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    (async () => {
      const token = await secureStore.getToken();
      if (token) {
        dispatch(setToken(token));
        try {
          await dispatch(fetchCurrentUser() as any).unwrap();
        } catch (err) {
          // if fetching user fails, clear token
          dispatch(setToken(null));
        }
      }
    })();
  }, []);

  useEffect(() => {
    if (language) {
      i18nSetLanguage(language as 'zh' | 'en');
    }
  }, [language]);

  return <AppNavigator />;
};

export default function App() {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <SafeAreaProvider>
          <AppWithLanguageSync />
          <StatusBar style="auto" />
        </SafeAreaProvider>
      </PersistGate>
    </Provider>
  );
}
