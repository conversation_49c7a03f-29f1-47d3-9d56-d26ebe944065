# 数据库配置
# 如果有本地MongoDB，使用: mongodb://localhost:27017/healthylife
# 如果使用MongoDB Atlas，使用: mongodb+srv://username:<EMAIL>/healthylife
MONGODB_URI=mongodb://***************:27017/healthylife
DATABASE_NAME=healthylife

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# 应用配置
PORT=3000
NODE_ENV=development

# OpenAI配置
OPENAI_API_KEY=your-openai-api-key-here

# Google OAuth配置 (可选)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback

# Apple OAuth配置 (可选)
APPLE_CLIENT_ID=your-apple-client-id
APPLE_TEAM_ID=your-apple-team-id
APPLE_KEY_ID=your-apple-key-id
APPLE_PRIVATE_KEY_PATH=./certs/apple-private-key.p8

# 应用安全
BCRYPT_SALT_ROUNDS=12
CORS_ORIGIN=http://localhost:8081,exp://localhost:8081

# 推送通知配置 (可选)
EXPO_ACCESS_TOKEN=your-expo-access-token

# 邮件服务配置 (可选，用于发送验证码)
# Gmail示例配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 短信服务配置 (可选，用于手机验证码)
# 支持的服务商: aliyun, tencent, huawei
SMS_PROVIDER=aliyun
SMS_API_KEY=your-sms-api-key
SMS_API_SECRET=your-sms-api-secret