# HealthyLife 后端项目设置指南

## 快速启动

### 1. 数据库设置
**选项A: 本地MongoDB**
```bash
# 安装并启动MongoDB
mongod --dbpath ./data/db
```

**选项B: MongoDB Atlas云数据库**
- 访问 https://cloud.mongodb.com
- 创建免费集群
- 获取连接字符串
- 更新.env中的MONGODB_URI

### 2. 环境变量配置
复制.env文件并根据需要修改：
- MONGODB_URI: 数据库连接字符串
- JWT_SECRET: 更改为安全的密钥
- OPENAI_API_KEY: 用于AI建议功能

### 3. 启动服务
```bash
npm run start:dev
```

### 4. 访问API文档
http://localhost:3000/api/docs

## 功能模块
✅ 用户认证（JWT）
✅ 体脂数据管理
✅ 设备管理
✅ AI智能建议
✅ 完整的RESTful API