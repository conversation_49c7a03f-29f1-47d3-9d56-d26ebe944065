{"name": "healthylife", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^15.0.2", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "@reduxjs/toolkit": "^2.9.0", "@types/i18n-js": "^4.0.1", "axios": "^1.12.2", "expo": "~54.0.7", "expo-auth-session": "^7.0.8", "expo-constants": "^18.0.8", "expo-crypto": "^15.0.7", "expo-device": "^8.0.7", "expo-localization": "^17.0.7", "expo-notifications": "^0.32.11", "expo-secure-store": "^15.0.7", "expo-status-bar": "~3.0.8", "i18n-js": "^4.5.1", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.4", "react-native-ble-plx": "^3.5.0", "react-native-chart-kit": "^6.12.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0", "react-native-svg": "15.13.0", "react-native-web": "^0.21.0", "react-redux": "^9.2.0"}, "optionalDependencies": {"@react-native-async-storage/async-storage": "^1.24.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@types/react": "~19.1.0", "typescript": "~5.9.2"}, "private": true}