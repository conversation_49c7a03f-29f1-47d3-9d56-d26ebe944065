import { <PERSON>du<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SuggestionsController } from './suggestions.controller';
import { SuggestionsService } from './suggestions.service';
import { Suggestion, SuggestionSchema } from '../schemas/suggestion.schema';
import { ChatGPTService } from '../common/services/chatgpt.service';
import { UsersModule } from '../users/users.module';
import { BodyDataModule } from '../body-data/body-data.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Suggestion.name, schema: SuggestionSchema }]),
    UsersModule,
    BodyDataModule,
  ],
  controllers: [SuggestionsController],
  providers: [SuggestionsService, ChatGPTService],
  exports: [SuggestionsService, ChatGPTService],
})
export class SuggestionsModule {}