import { IsNotEmpty, IsEnum, IsOptional, IsString, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateSuggestionDto {
  @ApiProperty({ 
    description: '建议类型', 
    example: 'health',
    enum: ['health', 'exercise', 'nutrition', 'trend_analysis'] 
  })
  @IsNotEmpty({ message: '建议类型不能为空' })
  @IsEnum(['health', 'exercise', 'nutrition', 'trend_analysis'], { 
    message: '建议类型无效' 
  })
  suggestionType: 'health' | 'exercise' | 'nutrition' | 'trend_analysis';

  @ApiProperty({ 
    description: '建议内容', 
    example: '根据您的体脂数据，建议增加有氧运动...',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '建议内容必须是字符串' })
  content?: string;

  @ApiProperty({ 
    description: '建议来源', 
    example: 'ai_generated',
    enum: ['ai_generated', 'manual', 'system'],
    required: false,
    default: 'ai_generated'
  })
  @IsOptional()
  @IsEnum(['ai_generated', 'manual', 'system'], { message: '建议来源无效' })
  source?: 'ai_generated' | 'manual' | 'system';

  @ApiProperty({ 
    description: '相关的体脂数据ID', 
    example: '507f1f77bcf86cd799439011',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '体脂数据ID必须是字符串' })
  relatedBodyDataId?: string;

  @ApiProperty({ 
    description: '建议优先级', 
    example: 'medium',
    enum: ['low', 'medium', 'high'],
    required: false,
    default: 'medium'
  })
  @IsOptional()
  @IsEnum(['low', 'medium', 'high'], { message: '优先级无效' })
  priority?: 'low' | 'medium' | 'high';

  @ApiProperty({ 
    description: '建议标签', 
    example: ['减脂', '运动', '饮食'],
    required: false 
  })
  @IsOptional()
  tags?: string[];

  @ApiProperty({ 
    description: '建议元数据', 
    example: { confidence: 0.85, model_version: 'gpt-3.5-turbo' },
    required: false 
  })
  @IsOptional()
  @IsObject({ message: '元数据必须是对象' })
  metadata?: Record<string, any>;
}