import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Suggestion, SuggestionDocument } from '../schemas/suggestion.schema';
import { CreateSuggestionDto } from './dto/create-suggestion.dto';
import { ChatGPTService } from '../common/services/chatgpt.service';
import { UsersService } from '../users/users.service';
import { BodyDataService } from '../body-data/body-data.service';

@Injectable()
export class SuggestionsService {
  constructor(
    @InjectModel(Suggestion.name) private suggestionModel: Model<SuggestionDocument>,
    private chatGPTService: ChatGPTService,
    private usersService: UsersService,
    private bodyDataService: BodyDataService,
  ) {}

  /**
   * 创建建议记录
   */
  async create(userId: string, createSuggestionDto: CreateSuggestionDto): Promise<Suggestion> {
    const newSuggestion = new this.suggestionModel({
      ...createSuggestionDto,
      userId,
      generatedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return newSuggestion.save();
  }

  /**
   * 生成AI健康建议
   */
  async generateHealthAdvice(userId: string): Promise<Suggestion> {
    try {
      // 获取用户信息和最近的体脂数据
      const user = await this.usersService.findOne(userId);
      const bodyDataResult = await this.bodyDataService.findAll(userId, 1, 5);
      
      // 使用ChatGPT生成建议
      const advice = await this.chatGPTService.generateHealthAdvice(
        user, 
        bodyDataResult.data
      );

      // 保存生成的建议
      const suggestion = await this.create(userId, {
        suggestionType: 'health',
        content: advice,
        source: 'ai_generated',
        priority: 'medium',
        tags: ['健康', 'AI建议'],
        metadata: {
          model: 'gpt-3.5-turbo',
          generated_at: new Date().toISOString(),
          data_points: bodyDataResult.data.length,
        },
      });

      return suggestion;
    } catch (error) {
      throw new Error(`生成健康建议失败: ${error.message}`);
    }
  }

  /**
   * 生成AI运动建议
   */
  async generateExerciseAdvice(userId: string): Promise<Suggestion> {
    try {
      const user = await this.usersService.findOne(userId);
      const bodyDataResult = await this.bodyDataService.findAll(userId, 1, 5);
      
      const advice = await this.chatGPTService.generateExerciseAdvice(
        user, 
        bodyDataResult.data
      );

      const suggestion = await this.create(userId, {
        suggestionType: 'exercise',
        content: advice,
        source: 'ai_generated',
        priority: 'medium',
        tags: ['运动', '健身', 'AI建议'],
        metadata: {
          model: 'gpt-3.5-turbo',
          generated_at: new Date().toISOString(),
          data_points: bodyDataResult.data.length,
        },
      });

      return suggestion;
    } catch (error) {
      throw new Error(`生成运动建议失败: ${error.message}`);
    }
  }

  /**
   * 生成AI营养建议
   */
  async generateNutritionAdvice(userId: string): Promise<Suggestion> {
    try {
      const user = await this.usersService.findOne(userId);
      const bodyDataResult = await this.bodyDataService.findAll(userId, 1, 5);
      
      const advice = await this.chatGPTService.generateNutritionAdvice(
        user, 
        bodyDataResult.data
      );

      const suggestion = await this.create(userId, {
        suggestionType: 'nutrition',
        content: advice,
        source: 'ai_generated',
        priority: 'medium',
        tags: ['营养', '饮食', 'AI建议'],
        metadata: {
          model: 'gpt-3.5-turbo',
          generated_at: new Date().toISOString(),
          data_points: bodyDataResult.data.length,
        },
      });

      return suggestion;
    } catch (error) {
      throw new Error(`生成营养建议失败: ${error.message}`);
    }
  }

  /**
   * 生成数据趋势分析
   */
  async generateTrendAnalysis(userId: string): Promise<Suggestion> {
    try {
      const bodyDataResult = await this.bodyDataService.findAll(userId, 1, 10);
      
      if (bodyDataResult.data.length < 2) {
        throw new Error('数据不足，无法进行趋势分析');
      }

      const analysis = await this.chatGPTService.analyzeBodyDataTrend(bodyDataResult.data);

      const suggestion = await this.create(userId, {
        suggestionType: 'trend_analysis',
        content: analysis,
        source: 'ai_generated',
        priority: 'high',
        tags: ['趋势分析', '数据分析', 'AI建议'],
        metadata: {
          model: 'gpt-3.5-turbo',
          generated_at: new Date().toISOString(),
          data_points: bodyDataResult.data.length,
          analysis_period: '最近10条记录',
        },
      });

      return suggestion;
    } catch (error) {
      throw new Error(`生成趋势分析失败: ${error.message}`);
    }
  }

  /**
   * 批量生成所有类型的AI建议
   */
  async generateAllAdvice(userId: string): Promise<{
    health: Suggestion;
    exercise: Suggestion;
    nutrition: Suggestion;
    trendAnalysis?: Suggestion;
  }> {
    try {
      const [health, exercise, nutrition] = await Promise.all([
        this.generateHealthAdvice(userId),
        this.generateExerciseAdvice(userId),
        this.generateNutritionAdvice(userId),
      ]);

      let trendAnalysis: Suggestion | undefined;
      try {
        trendAnalysis = await this.generateTrendAnalysis(userId);
      } catch (error) {
        // 如果数据不足，趋势分析可能失败，这是正常的
        console.log('趋势分析生成失败:', error.message);
      }

      return {
        health,
        exercise,
        nutrition,
        trendAnalysis,
      };
    } catch (error) {
      throw new Error(`批量生成建议失败: ${error.message}`);
    }
  }

  /**
   * 获取用户的建议列表
   */
  async findAll(
    userId: string,
    page: number = 1,
    limit: number = 20,
    suggestionType?: string,
    priority?: string,
  ): Promise<{ data: Suggestion[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;
    const filter: any = { userId };

    if (suggestionType) {
      filter.suggestionType = suggestionType;
    }

    if (priority) {
      filter.priority = priority;
    }

    const [data, total] = await Promise.all([
      this.suggestionModel
        .find(filter)
        .sort({ generatedAt: -1, createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('relatedBodyDataId', 'weight bodyFat measuredAt')
        .exec(),
      this.suggestionModel.countDocuments(filter),
    ]);

    return {
      data,
      total,
      page,
      limit,
    };
  }

  /**
   * 获取单条建议
   */
  async findOne(id: string, userId: string): Promise<Suggestion> {
    const suggestion = await this.suggestionModel
      .findById(id)
      .populate('relatedBodyDataId', 'weight bodyFat measuredAt')
      .exec();

    if (!suggestion) {
      throw new NotFoundException('建议记录不存在');
    }

    if (suggestion.userId.toString() !== userId) {
      throw new ForbiddenException('无权访问此建议');
    }

    return suggestion;
  }

  /**
   * 获取最新的建议
   */
  async getLatestByType(
    userId: string, 
    suggestionType: string
  ): Promise<Suggestion | null> {
    return this.suggestionModel
      .findOne({ userId, suggestionType })
      .sort({ generatedAt: -1, createdAt: -1 })
      .populate('relatedBodyDataId', 'weight bodyFat measuredAt')
      .exec();
  }

  /**
   * 标记建议为已读
   */
  async markAsRead(id: string, userId: string): Promise<Suggestion> {
    const suggestion = await this.suggestionModel.findById(id);

    if (!suggestion) {
      throw new NotFoundException('建议记录不存在');
    }

    if (suggestion.userId.toString() !== userId) {
      throw new ForbiddenException('无权修改此建议');
    }

    const updatedSuggestion = await this.suggestionModel.findByIdAndUpdate(
      id,
      {
        isRead: true,
        readAt: new Date(),
        updatedAt: new Date(),
      },
      { new: true },
    ).populate('relatedBodyDataId', 'weight bodyFat measuredAt');

    return updatedSuggestion;
  }

  /**
   * 删除建议
   */
  async remove(id: string, userId: string): Promise<{ message: string }> {
    const suggestion = await this.suggestionModel.findById(id);

    if (!suggestion) {
      throw new NotFoundException('建议记录不存在');
    }

    if (suggestion.userId.toString() !== userId) {
      throw new ForbiddenException('无权删除此建议');
    }

    await this.suggestionModel.findByIdAndDelete(id);

    return { message: '建议删除成功' };
  }

  /**
   * 获取建议统计信息
   */
  async getStatistics(userId: string): Promise<any> {
    const totalSuggestions = await this.suggestionModel.countDocuments({ userId });
    const unreadSuggestions = await this.suggestionModel.countDocuments({ 
      userId, 
      isRead: false 
    });

    // 按类型统计
    const suggestionsByType = await this.suggestionModel.aggregate([
      { $match: { userId: userId } },
      { $group: { _id: '$suggestionType', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // 按优先级统计
    const suggestionsByPriority = await this.suggestionModel.aggregate([
      { $match: { userId: userId } },
      { $group: { _id: '$priority', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // 最近的建议
    const recentSuggestions = await this.suggestionModel
      .find({ userId })
      .sort({ generatedAt: -1, createdAt: -1 })
      .limit(5)
      .select('suggestionType priority generatedAt isRead')
      .exec();

    return {
      totalSuggestions,
      unreadSuggestions,
      readRate: totalSuggestions > 0 ? 
        ((totalSuggestions - unreadSuggestions) / totalSuggestions * 100).toFixed(1) + '%' : '0%',
      suggestionsByType: suggestionsByType.map(item => ({
        type: item._id,
        count: item.count,
      })),
      suggestionsByPriority: suggestionsByPriority.map(item => ({
        priority: item._id,
        count: item.count,
      })),
      recentSuggestions,
    };
  }
}