import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  UseGuards, 
  Request,
  Query,
  ParseIntPipe,
  DefaultValuePipe
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { SuggestionsService } from './suggestions.service';
import { CreateSuggestionDto } from './dto/create-suggestion.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('AI建议')
@Controller('suggestions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SuggestionsController {
  constructor(private readonly suggestionsService: SuggestionsService) {}

  @Post()
  @ApiOperation({ 
    summary: '创建建议记录', 
    description: '手动创建建议记录' 
  })
  @ApiResponse({ status: 201, description: '创建成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async create(@Request() req, @Body() createSuggestionDto: CreateSuggestionDto) {
    const userId = req.user._id.toString();
    return this.suggestionsService.create(userId, createSuggestionDto);
  }

  @Post('generate/all')
  @ApiOperation({ 
    summary: '生成所有类型的AI建议', 
    description: '一次性生成健康、运动、营养建议和趋势分析' 
  })
  @ApiResponse({ status: 201, description: '生成成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  @ApiResponse({ status: 500, description: 'AI服务异常' })
  async generateAllAdvice(@Request() req) {
    const userId = req.user._id.toString();
    return this.suggestionsService.generateAllAdvice(userId);
  }

  @Post('generate/health')
  @ApiOperation({ 
    summary: '生成健康建议', 
    description: '基于用户体脂数据生成个性化健康建议' 
  })
  @ApiResponse({ status: 201, description: '生成成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  @ApiResponse({ status: 500, description: 'AI服务异常' })
  async generateHealthAdvice(@Request() req) {
    const userId = req.user._id.toString();
    return this.suggestionsService.generateHealthAdvice(userId);
  }

  @Post('generate/exercise')
  @ApiOperation({ 
    summary: '生成运动建议', 
    description: '基于用户体脂数据生成个性化运动建议' 
  })
  @ApiResponse({ status: 201, description: '生成成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  @ApiResponse({ status: 500, description: 'AI服务异常' })
  async generateExerciseAdvice(@Request() req) {
    const userId = req.user._id.toString();
    return this.suggestionsService.generateExerciseAdvice(userId);
  }

  @Post('generate/nutrition')
  @ApiOperation({ 
    summary: '生成营养建议', 
    description: '基于用户体脂数据生成个性化营养建议' 
  })
  @ApiResponse({ status: 201, description: '生成成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  @ApiResponse({ status: 500, description: 'AI服务异常' })
  async generateNutritionAdvice(@Request() req) {
    const userId = req.user._id.toString();
    return this.suggestionsService.generateNutritionAdvice(userId);
  }

  @Post('generate/trend-analysis')
  @ApiOperation({ 
    summary: '生成趋势分析', 
    description: '分析用户体脂数据变化趋势并生成分析报告' 
  })
  @ApiResponse({ status: 201, description: '生成成功' })
  @ApiResponse({ status: 400, description: '数据不足' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  @ApiResponse({ status: 500, description: 'AI服务异常' })
  async generateTrendAnalysis(@Request() req) {
    const userId = req.user._id.toString();
    return this.suggestionsService.generateTrendAnalysis(userId);
  }

  @Get()
  @ApiOperation({ 
    summary: '获取建议列表', 
    description: '获取用户的建议列表，支持分页和筛选' 
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码，默认为1' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: '每页数量，默认为20' })
  @ApiQuery({ name: 'type', required: false, description: '建议类型筛选' })
  @ApiQuery({ name: 'priority', required: false, description: '优先级筛选' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async findAll(
    @Request() req,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query('type') suggestionType?: string,
    @Query('priority') priority?: string,
  ) {
    const userId = req.user._id.toString();
    return this.suggestionsService.findAll(userId, page, limit, suggestionType, priority);
  }

  @Get('statistics')
  @ApiOperation({ 
    summary: '获取建议统计信息', 
    description: '获取用户建议的统计信息，包括总数、未读数、类型分布等' 
  })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async getStatistics(@Request() req) {
    const userId = req.user._id.toString();
    return this.suggestionsService.getStatistics(userId);
  }

  @Get('latest/:type')
  @ApiOperation({ 
    summary: '获取指定类型的最新建议', 
    description: '获取指定类型的最新一条建议' 
  })
  @ApiParam({ 
    name: 'type', 
    description: '建议类型', 
    enum: ['health', 'exercise', 'nutrition', 'trend_analysis'] 
  })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '建议不存在' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async getLatestByType(@Param('type') suggestionType: string, @Request() req) {
    const userId = req.user._id.toString();
    return this.suggestionsService.getLatestByType(userId, suggestionType);
  }

  @Get(':id')
  @ApiOperation({ 
    summary: '获取单条建议', 
    description: '根据ID获取特定的建议记录' 
  })
  @ApiParam({ name: 'id', description: '建议记录ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '建议不存在' })
  @ApiResponse({ status: 403, description: '无权访问此建议' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async findOne(@Param('id') id: string, @Request() req) {
    const userId = req.user._id.toString();
    return this.suggestionsService.findOne(id, userId);
  }

  @Patch(':id/read')
  @ApiOperation({ 
    summary: '标记建议为已读', 
    description: '将指定建议标记为已读状态' 
  })
  @ApiParam({ name: 'id', description: '建议记录ID' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '建议不存在' })
  @ApiResponse({ status: 403, description: '无权修改此建议' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async markAsRead(@Param('id') id: string, @Request() req) {
    const userId = req.user._id.toString();
    return this.suggestionsService.markAsRead(id, userId);
  }

  @Delete(':id')
  @ApiOperation({ 
    summary: '删除建议', 
    description: '删除指定的建议记录' 
  })
  @ApiParam({ name: 'id', description: '建议记录ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '建议不存在' })
  @ApiResponse({ status: 403, description: '无权删除此建议' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async remove(@Param('id') id: string, @Request() req) {
    const userId = req.user._id.toString();
    return this.suggestionsService.remove(id, userId);
  }
}