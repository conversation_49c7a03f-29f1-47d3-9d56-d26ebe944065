import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  UseGuards, 
  Request,
  Query
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { DevicesService } from './devices.service';
import { CreateDeviceDto } from './dto/create-device.dto';
import { UpdateDeviceDto } from './dto/update-device.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('设备管理')
@Controller('devices')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DevicesController {
  constructor(private readonly devicesService: DevicesService) {}

  @Post()
  @ApiOperation({ 
    summary: '添加新设备', 
    description: '为当前用户添加新的智能设备' 
  })
  @ApiBody({ type: CreateDeviceDto })
  @ApiResponse({ status: 201, description: '设备添加成功' })
  @ApiResponse({ status: 409, description: 'MAC地址已存在' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async create(@Request() req, @Body() createDeviceDto: CreateDeviceDto) {
    const userId = req.user._id.toString();
    return this.devicesService.create(userId, createDeviceDto);
  }

  @Get()
  @ApiOperation({ 
    summary: '获取设备列表', 
    description: '获取当前用户的所有设备列表' 
  })
  @ApiQuery({ name: 'type', required: false, description: '按设备类型筛选' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async findAll(@Request() req, @Query('type') deviceType?: string) {
    const userId = req.user._id.toString();
    
    if (deviceType) {
      return this.devicesService.findByType(userId, deviceType);
    }
    
    return this.devicesService.findAll(userId);
  }

  @Get('active')
  @ApiOperation({ 
    summary: '获取活跃设备', 
    description: '获取当前连接且状态为活跃的设备列表' 
  })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async getActiveDevices(@Request() req) {
    const userId = req.user._id.toString();
    return this.devicesService.getActiveDevices(userId);
  }

  @Get('statistics')
  @ApiOperation({ 
    summary: '获取设备统计信息', 
    description: '获取用户设备的统计信息，包括总数、类型分布等' 
  })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async getStatistics(@Request() req) {
    const userId = req.user._id.toString();
    return this.devicesService.getDeviceStatistics(userId);
  }

  @Post('pair')
  @ApiOperation({ 
    summary: '设备配对', 
    description: '通过MAC地址配对设备到当前用户' 
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        macAddress: { type: 'string', description: '设备MAC地址', example: 'AA:BB:CC:DD:EE:FF' },
        pairingCode: { type: 'string', description: '配对码（可选）', example: '123456' }
      },
      required: ['macAddress']
    }
  })
  @ApiResponse({ status: 200, description: '配对成功' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  @ApiResponse({ status: 409, description: '设备已被其他用户使用' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async pairDevice(
    @Request() req, 
    @Body() body: { macAddress: string; pairingCode?: string }
  ) {
    const userId = req.user._id.toString();
    const device = await this.devicesService.pairDevice(userId, body.macAddress, body.pairingCode);
    
    if (!device) {
      return { message: '设备不存在或无法配对' };
    }
    
    return { message: '设备配对成功', device };
  }

  @Get('mac/:macAddress')
  @ApiOperation({ 
    summary: '根据MAC地址查找设备', 
    description: '通过MAC地址查找用户的设备' 
  })
  @ApiParam({ name: 'macAddress', description: '设备MAC地址' })
  @ApiResponse({ status: 200, description: '查找成功' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async findByMacAddress(@Param('macAddress') macAddress: string, @Request() req) {
    const userId = req.user._id.toString();
    return this.devicesService.findByMacAddress(macAddress, userId);
  }

  @Get(':id')
  @ApiOperation({ 
    summary: '获取设备详情', 
    description: '根据设备ID获取设备的详细信息' 
  })
  @ApiParam({ name: 'id', description: '设备ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  @ApiResponse({ status: 403, description: '无权访问此设备' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async findOne(@Param('id') id: string, @Request() req) {
    const userId = req.user._id.toString();
    return this.devicesService.findOne(id, userId);
  }

  @Patch(':id')
  @ApiOperation({ 
    summary: '更新设备信息', 
    description: '更新指定设备的配置和信息' 
  })
  @ApiParam({ name: 'id', description: '设备ID' })
  @ApiBody({ type: UpdateDeviceDto })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  @ApiResponse({ status: 403, description: '无权修改此设备' })
  @ApiResponse({ status: 409, description: 'MAC地址冲突' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async update(
    @Param('id') id: string, 
    @Request() req,
    @Body() updateDeviceDto: UpdateDeviceDto
  ) {
    const userId = req.user._id.toString();
    return this.devicesService.update(id, userId, updateDeviceDto);
  }

  @Patch(':id/connection')
  @ApiOperation({ 
    summary: '更新设备连接状态', 
    description: '更新设备的连接状态和最后连接时间' 
  })
  @ApiParam({ name: 'id', description: '设备ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        isConnected: { type: 'boolean', description: '是否连接' },
        lastConnectedAt: { type: 'string', format: 'date-time', description: '最后连接时间（可选）' }
      },
      required: ['isConnected']
    }
  })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  @ApiResponse({ status: 403, description: '无权修改此设备' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async updateConnection(
    @Param('id') id: string,
    @Request() req,
    @Body() body: { isConnected: boolean; lastConnectedAt?: string }
  ) {
    const userId = req.user._id.toString();
    const lastConnectedAt = body.lastConnectedAt ? new Date(body.lastConnectedAt) : undefined;
    
    return this.devicesService.updateConnectionStatus(
      id, 
      userId, 
      body.isConnected, 
      lastConnectedAt
    );
  }

  @Delete(':id')
  @ApiOperation({ 
    summary: '删除设备', 
    description: '删除指定的设备记录' 
  })
  @ApiParam({ name: 'id', description: '设备ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  @ApiResponse({ status: 403, description: '无权删除此设备' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async remove(@Param('id') id: string, @Request() req) {
    const userId = req.user._id.toString();
    return this.devicesService.remove(id, userId);
  }
}