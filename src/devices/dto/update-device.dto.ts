import { PartialType } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CreateDeviceDto } from './create-device.dto';

export class UpdateDeviceDto extends PartialType(CreateDeviceDto) {
  @ApiProperty({ 
    description: '设备状态', 
    example: 'active',
    enum: ['active', 'inactive', 'maintenance'],
    required: false 
  })
  @IsOptional()
  @IsEnum(['active', 'inactive', 'maintenance'], { message: '设备状态无效' })
  status?: 'active' | 'inactive' | 'maintenance';
}