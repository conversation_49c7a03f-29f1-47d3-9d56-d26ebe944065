import { IsNotEmpty, IsString, IsOptional, IsEnum, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateDeviceDto {
  @ApiProperty({ 
    description: '设备名称', 
    example: '我的体脂秤' 
  })
  @IsNotEmpty({ message: '设备名称不能为空' })
  @IsString({ message: '设备名称必须是字符串' })
  name: string;

  @ApiProperty({ 
    description: '设备品牌', 
    example: 'Xiaomi' 
  })
  @IsNotEmpty({ message: '设备品牌不能为空' })
  @IsString({ message: '设备品牌必须是字符串' })
  brand: string;

  @ApiProperty({ 
    description: '设备型号', 
    example: 'Mi Body Composition Scale 2' 
  })
  @IsNotEmpty({ message: '设备型号不能为空' })
  @IsString({ message: '设备型号必须是字符串' })
  model: string;

  @ApiProperty({ 
    description: '设备类型', 
    example: 'body_scale',
    enum: ['body_scale', 'smart_watch', 'blood_pressure', 'thermometer', 'other'] 
  })
  @IsNotEmpty({ message: '设备类型不能为空' })
  @IsEnum(['body_scale', 'smart_watch', 'blood_pressure', 'thermometer', 'other'], { 
    message: '设备类型无效' 
  })
  deviceType: 'body_scale' | 'smart_watch' | 'blood_pressure' | 'thermometer' | 'other';

  @ApiProperty({ 
    description: '设备MAC地址', 
    example: 'AA:BB:CC:DD:EE:FF',
    required: false 
  })
  @IsOptional()
  @IsString({ message: 'MAC地址必须是字符串' })
  macAddress?: string;

  @ApiProperty({ 
    description: '设备序列号', 
    example: 'SN123456789',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '序列号必须是字符串' })
  serialNumber?: string;

  @ApiProperty({ 
    description: '固件版本', 
    example: '1.2.3',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '固件版本必须是字符串' })
  firmwareVersion?: string;

  @ApiProperty({ 
    description: '设备配置参数', 
    example: { unit: 'kg', precision: 2, autoSync: true },
    required: false 
  })
  @IsOptional()
  @IsObject({ message: '配置参数必须是对象' })
  configuration?: Record<string, any>;

  @ApiProperty({ 
    description: '设备描述', 
    example: '卧室使用的智能体脂秤',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '描述必须是字符串' })
  description?: string;
}