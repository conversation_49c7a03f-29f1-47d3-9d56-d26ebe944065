import { Injectable, NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Device, DeviceDocument } from '../schemas/device.schema';
import { CreateDeviceDto } from './dto/create-device.dto';
import { UpdateDeviceDto } from './dto/update-device.dto';

@Injectable()
export class DevicesService {
  constructor(
    @InjectModel(Device.name) private deviceModel: Model<DeviceDocument>,
  ) {}

  /**
   * 添加新设备
   */
  async create(userId: string, createDeviceDto: CreateDeviceDto): Promise<Device> {
    // 检查MAC地址是否已存在（如果提供了MAC地址）
    if (createDeviceDto.macAddress) {
      const existingDevice = await this.deviceModel.findOne({
        macAddress: createDeviceDto.macAddress.toUpperCase(),
      });
      
      if (existingDevice) {
        throw new ConflictException('该MAC地址的设备已存在');
      }
    }

    const newDevice = new this.deviceModel({
      ...createDeviceDto,
      userId,
      macAddress: createDeviceDto.macAddress?.toUpperCase(),
      status: 'active',
      lastConnectedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return newDevice.save();
  }

  /**
   * 获取用户所有设备
   */
  async findAll(userId: string): Promise<Device[]> {
    return this.deviceModel
      .find({ userId })
      .sort({ createdAt: -1 })
      .exec();
  }

  /**
   * 根据设备类型获取设备
   */
  async findByType(userId: string, deviceType: string): Promise<Device[]> {
    return this.deviceModel
      .find({ userId, deviceType })
      .sort({ createdAt: -1 })
      .exec();
  }

  /**
   * 获取单个设备信息
   */
  async findOne(id: string, userId: string): Promise<Device> {
    const device = await this.deviceModel.findById(id).exec();

    if (!device) {
      throw new NotFoundException('设备不存在');
    }

    if (device.userId.toString() !== userId) {
      throw new ForbiddenException('无权访问此设备');
    }

    return device;
  }

  /**
   * 根据MAC地址查找设备
   */
  async findByMacAddress(macAddress: string, userId: string): Promise<Device | null> {
    return this.deviceModel
      .findOne({ 
        macAddress: macAddress.toUpperCase(), 
        userId 
      })
      .exec();
  }

  /**
   * 更新设备信息
   */
  async update(id: string, userId: string, updateDeviceDto: UpdateDeviceDto): Promise<Device> {
    const device = await this.deviceModel.findById(id);

    if (!device) {
      throw new NotFoundException('设备不存在');
    }

    if (device.userId.toString() !== userId) {
      throw new ForbiddenException('无权修改此设备');
    }

    // 如果更新蓝牙ID，检查是否与其他设备冲突
    if (updateDeviceDto.macAddress && updateDeviceDto.macAddress !== device.bluetoothId) {
      const existingDevice = await this.deviceModel.findOne({
        macAddress: updateDeviceDto.macAddress.toUpperCase(),
        _id: { $ne: id },
      });
      
      if (existingDevice) {
        throw new ConflictException('该MAC地址的设备已存在');
      }
    }

    const updatedDevice = await this.deviceModel.findByIdAndUpdate(
      id,
      {
        ...updateDeviceDto,
        bluetoothId: updateDeviceDto.macAddress?.toUpperCase() || device.bluetoothId,
        updatedAt: new Date(),
      },
      { new: true },
    );

    return updatedDevice;
  }

  /**
   * 删除设备
   */
  async remove(id: string, userId: string): Promise<{ message: string }> {
    const device = await this.deviceModel.findById(id);

    if (!device) {
      throw new NotFoundException('设备不存在');
    }

    if (device.userId.toString() !== userId) {
      throw new ForbiddenException('无权删除此设备');
    }

    await this.deviceModel.findByIdAndDelete(id);

    return { message: '设备删除成功' };
  }

  /**
   * 更新设备连接状态
   */
  async updateConnectionStatus(
    id: string, 
    userId: string, 
    isConnected: boolean,
    lastConnectedAt?: Date
  ): Promise<Device> {
    const device = await this.deviceModel.findById(id);

    if (!device) {
      throw new NotFoundException('设备不存在');
    }

    if (device.userId.toString() !== userId) {
      throw new ForbiddenException('无权修改此设备');
    }

    const updateData: any = {
      isConnected,
      updatedAt: new Date(),
    };

    if (lastConnectedAt) {
      updateData.lastConnectedAt = lastConnectedAt;
    } else if (isConnected) {
      updateData.lastConnectedAt = new Date();
    }

    const updatedDevice = await this.deviceModel.findByIdAndUpdate(
      id,
      updateData,
      { new: true },
    );

    return updatedDevice;
  }

  /**
   * 获取活跃设备列表
   */
  async getActiveDevices(userId: string): Promise<Device[]> {
    return this.deviceModel
      .find({ 
        userId, 
        status: 'active',
        isConnected: true 
      })
      .sort({ lastConnectedAt: -1 })
      .exec();
  }

  /**
   * 获取设备使用统计
   */
  async getDeviceStatistics(userId: string): Promise<any> {
    const totalDevices = await this.deviceModel.countDocuments({ userId });
    const activeDevices = await this.deviceModel.countDocuments({ 
      userId, 
      status: 'active' 
    });
    const connectedDevices = await this.deviceModel.countDocuments({ 
      userId, 
      isConnected: true 
    });

    // 按设备类型统计
    const devicesByType = await this.deviceModel.aggregate([
      { $match: { userId: userId } },
      { $group: { _id: '$deviceType', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // 最近连接的设备
    const recentlyConnected = await this.deviceModel
      .find({ userId })
      .sort({ lastConnectedAt: -1 })
      .limit(5)
      .select('name brand model deviceType lastConnectedAt')
      .exec();

    return {
      totalDevices,
      activeDevices,
      connectedDevices,
      devicesByType: devicesByType.map(item => ({
        type: item._id,
        count: item.count,
      })),
      recentlyConnected,
    };
  }

  /**
   * 设备配对验证
   */
  async pairDevice(
    userId: string, 
    macAddress: string, 
    pairingCode?: string
  ): Promise<Device | null> {
    // 查找未配对的设备或者验证配对码
    const device = await this.deviceModel.findOne({
      macAddress: macAddress.toUpperCase(),
      $or: [
        { userId: null },
        { userId: userId }
      ]
    });

    if (!device) {
      return null;
    }

    // 如果设备已被其他用户配对，需要验证配对码
    if (device.userId && device.userId.toString() !== userId) {
      if (!pairingCode) {
        throw new ConflictException('设备已被其他用户使用，需要配对码');
      }
      
      // 验证配对码
      const isValidPairingCode = await this.validatePairingCode(device, pairingCode);
      if (!isValidPairingCode) {
        throw new ConflictException('配对码错误');
      }
      
      // 配对成功，解除原有绑定
      await this.unbindDevice(device._id.toString());
    }

    // 配对设备到当前用户
    if (!device.userId) {
      device.userId = new Types.ObjectId(userId);
      device.lastConnectedAt = new Date();
      device.updatedAt = new Date();
      await device.save();
    }

    return device;
  }

  /**
   * 验证配对码
   */
  private async validatePairingCode(device: DeviceDocument, pairingCode: string): Promise<boolean> {
    // 实际项目中，配对码可以是:
    // 1. 设备上显示的动态码
    // 2. 设备序列号的后6位
    // 3. 用户手动设置的码
    // 4. 基于时间的动态码
    
    // 这里使用简化的验证逻辑：
    // 1. 检查是否为设备的默认配对码（蓝牙ID的后6位）
    const defaultCode = device.bluetoothId.slice(-6).toUpperCase();
    if (pairingCode.toUpperCase() === defaultCode) {
      return true;
    }
    
    // 2. 检查是否为通用配对码
    const commonCodes = ['123456', '000000', '888888', '666666'];
    if (commonCodes.includes(pairingCode)) {
      return true;
    }
    
    // 3. 检查是否为基于时间的动态码
    const timeBasedCode = this.generateTimeBasedCode(device.bluetoothId);
    if (pairingCode === timeBasedCode) {
      return true;
    }
    
    return false;
  }

  /**
   * 生成基于时间的动态配对码
   */
  private generateTimeBasedCode(deviceId: string): string {
    // 使用当前时间（取整分钟）和设备ID生成动态码
    const now = Math.floor(Date.now() / 60000); // 每分钟更新
    const hash = require('crypto').createHash('md5')
      .update(deviceId + now.toString())
      .digest('hex');
    return hash.slice(0, 6).toUpperCase();
  }

  /**
   * 解除设备绑定
   */
  private async unbindDevice(deviceId: string): Promise<void> {
    await this.deviceModel.findByIdAndUpdate(deviceId, {
      userId: null,
      status: 'inactive',
      lastConnectedAt: null,
      updatedAt: new Date(),
    });
  }
}