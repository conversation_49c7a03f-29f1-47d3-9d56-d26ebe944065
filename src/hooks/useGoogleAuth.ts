import { useEffect } from 'react';
import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';
import { User } from '../types';
import AuthService from '../services/AuthService';
import { Config } from '../config';
import { useState } from 'react';

// 确保在使用认证会话之前完成WebBrowser设置
WebBrowser.maybeCompleteAuthSession();

// Google OAuth 配置
const GOOGLE_CLIENT_ID = 'YOUR_GOOGLE_CLIENT_ID'; // 需要替换为真实的Google Client ID

/**
 * Google认证Hook
 * 使用expo-auth-session的新API (useAuthRequest)
 */
export const useGoogleAuth = () => {
  // 创建认证请求配置
  const [request, response, promptAsync] = AuthSession.useAuthRequest(
    {
      clientId: GOOGLE_CLIENT_ID,
      scopes: ['openid', 'profile', 'email'],
      responseType: AuthSession.ResponseType.Code,
      redirectUri: AuthSession.makeRedirectUri({
        scheme: 'healthylife', // 使用应用的scheme
      }),
    },
    {
      authorizationEndpoint: 'https://accounts.google.com/oauth/authorize',
    }
  );

  const [result, setResult] = useState<any | null>(null);

  // 处理认证响应
  useEffect(() => {
    if (response?.type === 'success') {
      const { code } = response.params;
      (async () => {
        try {
          // 将授权码发送给后端，由后端完成与 Google 的令牌交换并返回登录 payload
          const res = await fetch(`${Config.api.baseUrl}/auth/exchange-google-code`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ code, redirectUri: AuthSession.makeRedirectUri({ scheme: 'healthylife' }) }),
          });
          if (!res.ok) {
            const text = await res.text();
            throw new Error(text || '服务器端交换Google令牌失败');
          }
          const payload = await res.json();
          setResult(payload);
        } catch (err) {
          console.error('Google 认证/登录失败:', err);
          setResult({ error: err instanceof Error ? err.message : String(err) });
        }
      })();
    }
  }, [response]);

  /**
   * 处理Google认证成功
   */
  const handleGoogleAuthSuccess = async (code: string): Promise<User> => {
    try {
      // 用授权码换取访问令牌
      const tokenResponse = await fetch('https://your-api.com/auth/google', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          code, 
          redirectUri: AuthSession.makeRedirectUri({ scheme: 'healthylife' }) 
        }),
      });

      if (!tokenResponse.ok) {
        throw new Error('Failed to exchange code for token');
      }

      const userData = await tokenResponse.json();
      return {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        avatar: userData.avatar,
        authProvider: 'google',
        createdAt: new Date(userData.createdAt),
      };
    } catch (error) {
      console.error('Google认证失败:', error);
      // 开发阶段返回模拟数据
      return {
        id: '1',
        email: '<EMAIL>',
        name: 'Google用户',
        authProvider: 'google',
        createdAt: new Date(),
        phone: undefined,
        gender: 'other',
        birthday: new Date('1988-03-20'),
        height: 170,
        targetWeight: 65,
        activityLevel: 'very_active',
        preferences: {
          units: 'metric',
          language: 'zh',
          notifications: {
            dataReminder: true,
            goalAchievement: true,
            suggestions: false,
          },
        },
      };
    }
  };

  /**
   * 启动Google登录流程
   */
  const loginWithGoogle = async (): Promise<void> => {
    if (!request) {
      console.warn('Google认证请求未准备就绪');
      return;
    }

    try {
      await promptAsync();
    } catch (error) {
      console.error('启动Google认证失败:', error);
      throw new Error('Google登录失败');
    }
  };

  return {
    loginWithGoogle,
    request,
    response,
    result,
    isLoading: !request,
  };
};