import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../store';
import { addLocalData } from '../store/slices/bodyDataSlice';
import BluetoothService from '../services/BluetoothService';
import { BodyCompositionData } from '../types';

// 蓝牙连接状态Hook
export const useBluetoothConnection = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [isReceivingData, setIsReceivingData] = useState(false);
  const connectedDevices = useSelector((state: RootState) => state.devices.connectedDevices);
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    setIsConnected(connectedDevices.length > 0);
  }, [connectedDevices]);

  // 开始监听体脂数据
  const startDataMonitoring = async () => {
    if (!isConnected || !user) {
      console.log('设备未连接或用户未登录');
      return;
    }

    try {
      setIsReceivingData(true);
      await BluetoothService.startDataMonitoring((data) => {
        // 收到新的体脂数据
        const bodyData: BodyCompositionData = {
          id: Date.now().toString(),
          userId: user.id,
          timestamp: data.timestamp || new Date(),
          weight: data.weight || 0,
          bodyFatPercentage: data.bodyFatPercentage || 0,
          muscleMass: data.muscleMass || 0,
          boneMass: data.boneMass || 0,
          waterPercentage: data.waterPercentage || 0,
          visceralFat: data.visceralFat || 0,
          bmr: data.bmr || 0,
          bmi: data.bmi || 0,
          deviceId: data.deviceId || '',
        };

        // 添加到Redux store
        dispatch(addLocalData(bodyData));
        console.log('收到新的体脂数据:', bodyData);
      });
    } catch (error) {
      console.error('开始数据监听失败:', error);
    } finally {
      setIsReceivingData(false);
    }
  };

  // 停止数据监听
  const stopDataMonitoring = async () => {
    try {
      await BluetoothService.stopDataMonitoring();
      setIsReceivingData(false);
    } catch (error) {
      console.error('停止数据监听失败:', error);
    }
  };

  return {
    isConnected,
    isReceivingData,
    startDataMonitoring,
    stopDataMonitoring,
  };
};