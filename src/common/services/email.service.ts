import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor(private configService: ConfigService) {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    const emailConfig = {
      host: this.configService.get('SMTP_HOST', 'smtp.gmail.com'),
      port: this.configService.get('SMTP_PORT', 587),
      secure: false, // true for 465, false for other ports
      auth: {
        user: this.configService.get('SMTP_USER'),
        pass: this.configService.get('SMTP_PASS'),
      },
    };

    // 如果没有配置邮件服务，使用测试模式
    if (!emailConfig.auth.user || !emailConfig.auth.pass) {
      this.logger.warn('邮件服务未配置，将使用测试模式');
      this.transporter = nodemailer.createTransport({
        streamTransport: true,
        newline: 'unix',
        buffer: true,
      });
    } else {
      this.transporter = nodemailer.createTransport(emailConfig);
    }
  }

  /**
   * 发送验证码邮件
   */
  async sendVerificationCode(
    email: string, 
    code: string, 
    type: 'register' | 'reset_password' | 'login'
  ): Promise<void> {
    const typeMap = {
      register: '注册验证',
      reset_password: '密码重置',
      login: '登录验证',
    };

    const subject = `HealthyLife ${typeMap[type]}验证码`;
    const html = this.generateVerificationEmailTemplate(code, typeMap[type]);

    try {
      const info = await this.transporter.sendMail({
        from: `"HealthyLife" <${this.configService.get('SMTP_USER', '<EMAIL>')}>`,
        to: email,
        subject,
        html,
      });

      this.logger.log(`验证码邮件发送成功: ${email}, MessageId: ${info.messageId}`);
    } catch (error) {
      this.logger.error(`发送验证码邮件失败: ${email}`, error.stack);
      
      // 如果是测试模式，不抛出错误
      if (!this.configService.get('SMTP_USER')) {
        this.logger.log(`测试模式: 验证码 ${code} 已"发送"到 ${email}`);
        return;
      }
      
      throw new Error('邮件发送失败');
    }
  }

  /**
   * 生成验证码邮件模板
   */
  private generateVerificationEmailTemplate(code: string, type: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>HealthyLife 验证码</title>
        <style>
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f9f9f9;
          }
          .card {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          .logo {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
          }
          .code {
            font-size: 32px;
            font-weight: bold;
            color: #2196F3;
            text-align: center;
            padding: 20px;
            background-color: #f5f5f5;
            border-radius: 6px;
            letter-spacing: 4px;
            margin: 20px 0;
          }
          .info {
            color: #666;
            line-height: 1.6;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            color: #999;
            font-size: 12px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="card">
            <div class="header">
              <div class="logo">🏃‍♂️ HealthyLife</div>
              <h2>${type}验证码</h2>
            </div>
            
            <div class="info">
              <p>您好！</p>
              <p>您正在进行${type}操作，验证码为：</p>
            </div>
            
            <div class="code">${code}</div>
            
            <div class="info">
              <p><strong>重要提醒：</strong></p>
              <ul>
                <li>验证码有效期为 5 分钟</li>
                <li>请勿将验证码告诉他人</li>
                <li>如果这不是您的操作，请忽略此邮件</li>
              </ul>
            </div>
            
            <div class="footer">
              <p>此邮件由系统自动发送，请勿直接回复</p>
              <p>&copy; 2024 HealthyLife. All rights reserved.</p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 发送欢迎邮件
   */
  async sendWelcomeEmail(email: string, nickname: string): Promise<void> {
    const subject = '欢迎加入 HealthyLife！';
    const html = this.generateWelcomeEmailTemplate(nickname);

    try {
      await this.transporter.sendMail({
        from: `"HealthyLife" <${this.configService.get('SMTP_USER', '<EMAIL>')}>`,
        to: email,
        subject,
        html,
      });

      this.logger.log(`欢迎邮件发送成功: ${email}`);
    } catch (error) {
      this.logger.error(`发送欢迎邮件失败: ${email}`, error.stack);
      // 欢迎邮件发送失败不影响注册流程
    }
  }

  private generateWelcomeEmailTemplate(nickname: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>欢迎加入 HealthyLife</title>
        <style>
          .container { max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif; }
          .card { background: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
          .header { text-align: center; margin-bottom: 30px; }
          .logo { font-size: 24px; font-weight: bold; color: #4CAF50; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="card">
            <div class="header">
              <div class="logo">🏃‍♂️ HealthyLife</div>
              <h2>欢迎加入 HealthyLife！</h2>
            </div>
            <p>亲爱的 ${nickname}，</p>
            <p>恭喜您成功注册 HealthyLife 智能健康管理应用！</p>
            <p>开始您的健康管理之旅吧！</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}