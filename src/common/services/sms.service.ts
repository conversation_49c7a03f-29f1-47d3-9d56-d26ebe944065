import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class SmsService {
  private readonly logger = new Logger(SmsService.name);

  constructor(private configService: ConfigService) {}

  /**
   * 发送短信验证码
   * 这里使用模拟发送，实际项目中需要集成真实的短信服务商
   */
  async sendVerificationCode(
    phone: string, 
    code: string, 
    type: 'register' | 'reset_password' | 'login'
  ): Promise<void> {
    const typeMap = {
      register: '注册验证',
      reset_password: '密码重置', 
      login: '登录验证',
    };

    // 验证手机号格式
    if (!this.isValidPhoneNumber(phone)) {
      throw new Error('手机号格式不正确');
    }

    const message = `【HealthyLife】您的${typeMap[type]}验证码是：${code}，有效期5分钟，请勿泄露。`;

    try {
      // TODO: 这里应该集成真实的短信服务商API
      // 常用的短信服务商：阿里云、腾讯云、华为云等
      await this.sendSmsViaMockProvider(phone, message);
      
      this.logger.log(`短信验证码发送成功: ${phone}`);
    } catch (error) {
      this.logger.error(`发送短信验证码失败: ${phone}`, error.stack);
      throw new Error('短信发送失败');
    }
  }

  /**
   * 模拟短信发送（用于开发测试）
   */
  private async sendSmsViaMockProvider(phone: string, message: string): Promise<void> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 开发环境下直接输出到控制台
    if (this.configService.get('NODE_ENV') === 'development') {
      this.logger.log(`📱 模拟短信发送到 ${phone}: ${message}`);
      return;
    }

    // 生产环境需要配置真实的短信服务
    const smsProvider = this.configService.get('SMS_PROVIDER'); // 'aliyun', 'tencent', 'huawei'
    const smsApiKey = this.configService.get('SMS_API_KEY');
    const smsApiSecret = this.configService.get('SMS_API_SECRET');
    
    if (!smsProvider || !smsApiKey || !smsApiSecret) {
      throw new Error('短信服务未正确配置');
    }

    // 根据不同的服务商调用相应的API
    switch (smsProvider) {
      case 'aliyun':
        await this.sendViaAliyun(phone, message);
        break;
      case 'tencent':
        await this.sendViaTencent(phone, message);
        break;
      case 'huawei':
        await this.sendViaHuawei(phone, message);
        break;
      default:
        throw new Error(`不支持的短信服务商: ${smsProvider}`);
    }
  }

  /**
   * 阿里云短信发送（示例实现）
   */
  private async sendViaAliyun(phone: string, message: string): Promise<void> {
    // TODO: 集成阿里云短信服务
    // 参考文档: https://help.aliyun.com/document_detail/101414.html
    this.logger.log(`通过阿里云发送短信到 ${phone}: ${message}`);
  }

  /**
   * 腾讯云短信发送（示例实现）
   */
  private async sendViaTencent(phone: string, message: string): Promise<void> {
    // TODO: 集成腾讯云短信服务
    // 参考文档: https://cloud.tencent.com/document/product/382
    this.logger.log(`通过腾讯云发送短信到 ${phone}: ${message}`);
  }

  /**
   * 华为云短信发送（示例实现）
   */
  private async sendViaHuawei(phone: string, message: string): Promise<void> {
    // TODO: 集成华为云短信服务
    this.logger.log(`通过华为云发送短信到 ${phone}: ${message}`);
  }

  /**
   * 验证手机号格式
   */
  private isValidPhoneNumber(phone: string): boolean {
    // 中国大陆手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 发送通知短信
   */
  async sendNotificationSms(phone: string, content: string): Promise<void> {
    if (!this.isValidPhoneNumber(phone)) {
      throw new Error('手机号格式不正确');
    }

    const message = `【HealthyLife】${content}`;
    
    try {
      await this.sendSmsViaMockProvider(phone, message);
      this.logger.log(`通知短信发送成功: ${phone}`);
    } catch (error) {
      this.logger.error(`发送通知短信失败: ${phone}`, error.stack);
      throw new Error('短信发送失败');
    }
  }
}