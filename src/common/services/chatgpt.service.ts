import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface ChatGPTMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatGPTResponse {
  choices: Array<{
    message: ChatGPTMessage;
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

@Injectable()
export class ChatGPTService {
  private readonly apiKey: string;
  private readonly apiUrl = 'https://api.openai.com/v1/chat/completions';
  private readonly model: string;

  constructor(private configService: ConfigService) {
    this.apiKey = this.configService.get<string>('OPENAI_API_KEY');
    this.model = this.configService.get<string>('OPENAI_MODEL') || 'gpt-3.5-turbo';
  }

  /**
   * 发送消息到ChatGPT并获取回复
   */
  async sendMessage(messages: ChatGPTMessage[], options?: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
  }): Promise<string> {
    if (!this.apiKey) {
      throw new HttpException(
        'OpenAI API密钥未配置',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    try {
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.model,
          messages,
          temperature: options?.temperature || 0.7,
          max_tokens: options?.maxTokens || 1000,
          top_p: options?.topP || 1,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new HttpException(
          `OpenAI API错误: ${response.status} - ${errorData.error?.message || response.statusText}`,
          HttpStatus.BAD_REQUEST,
        );
      }

      const data: ChatGPTResponse = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new HttpException(
          'OpenAI API未返回有效响应',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      return data.choices[0].message.content;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        `调用OpenAI API失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 生成健康建议
   */
  async generateHealthAdvice(userProfile: any, bodyData: any[]): Promise<string> {
    const systemPrompt = `你是一名专业的健康顾问和营养师。请根据用户的个人资料和体脂数据，提供个性化的健康建议。

请遵循以下原则：
1. 基于科学的健康知识
2. 考虑用户的个人情况
3. 提供实用且可执行的建议
4. 避免医疗诊断和治疗建议
5. 建议如有健康问题请咨询专业医生
6. 回复需要简洁明了，控制在200字以内
7. 使用中文回复`;

    const userMessage = this.formatUserDataForAdvice(userProfile, bodyData);

    const messages: ChatGPTMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userMessage },
    ];

    return this.sendMessage(messages, {
      temperature: 0.7,
      maxTokens: 500,
    });
  }

  /**
   * 生成运动建议
   */
  async generateExerciseAdvice(userProfile: any, bodyData: any[]): Promise<string> {
    const systemPrompt = `你是一名专业的健身教练。请根据用户的体脂数据和个人资料，提供个性化的运动建议。

请提供：
1. 适合的运动类型
2. 运动强度和频率建议
3. 运动注意事项
4. 循序渐进的训练计划建议

回复需要简洁实用，控制在200字以内，使用中文回复。`;

    const userMessage = this.formatUserDataForAdvice(userProfile, bodyData);

    const messages: ChatGPTMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userMessage },
    ];

    return this.sendMessage(messages, {
      temperature: 0.6,
      maxTokens: 500,
    });
  }

  /**
   * 生成饮食建议
   */
  async generateNutritionAdvice(userProfile: any, bodyData: any[]): Promise<string> {
    const systemPrompt = `你是一名专业的营养师。请根据用户的体脂数据和个人资料，提供个性化的饮食建议。

请提供：
1. 营养摄入建议
2. 食物选择推荐
3. 饮食习惯建议
4. 热量控制建议

回复需要简洁实用，控制在200字以内，使用中文回复。`;

    const userMessage = this.formatUserDataForAdvice(userProfile, bodyData);

    const messages: ChatGPTMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userMessage },
    ];

    return this.sendMessage(messages, {
      temperature: 0.6,
      maxTokens: 500,
    });
  }

  /**
   * 分析体脂数据趋势
   */
  async analyzeBodyDataTrend(bodyData: any[]): Promise<string> {
    const systemPrompt = `你是一名专业的健康数据分析师。请分析用户的体脂数据变化趋势，并提供专业的分析和建议。

请提供：
1. 数据趋势分析
2. 变化原因分析
3. 改进建议
4. 激励性的评价

回复需要客观专业，控制在200字以内，使用中文回复。`;

    const trendData = this.formatBodyDataForTrendAnalysis(bodyData);

    const messages: ChatGPTMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: trendData },
    ];

    return this.sendMessage(messages, {
      temperature: 0.5,
      maxTokens: 500,
    });
  }

  /**
   * 格式化用户数据用于生成建议
   */
  private formatUserDataForAdvice(userProfile: any, bodyData: any[]): string {
    const profile = userProfile.profile || {};
    const latest = bodyData[0] || {};
    
    let message = `用户基本信息：
- 性别: ${profile.gender === 'male' ? '男' : profile.gender === 'female' ? '女' : '未知'}
- 年龄: ${profile.age || '未知'}岁
- 身高: ${profile.height || '未知'}cm`;

    if (latest.weight) {
      message += `\n\n最新体脂数据：
- 体重: ${latest.weight}kg`;
      
      if (latest.bodyFat) message += `\n- 体脂率: ${latest.bodyFat}%`;
      if (latest.muscleMass) message += `\n- 肌肉量: ${latest.muscleMass}kg`;
      if (latest.bmr) message += `\n- 基础代谢: ${latest.bmr}kcal`;
      if (latest.visceralFat) message += `\n- 内脏脂肪等级: ${latest.visceralFat}`;
    }

    if (bodyData.length > 1) {
      const previous = bodyData[1];
      const weightChange = latest.weight - previous.weight;
      message += `\n\n体重变化: ${weightChange > 0 ? '+' : ''}${weightChange.toFixed(1)}kg`;
    }

    return message;
  }

  /**
   * 格式化体脂数据用于趋势分析
   */
  private formatBodyDataForTrendAnalysis(bodyData: any[]): string {
    if (!bodyData || bodyData.length === 0) {
      return '暂无体脂数据';
    }

    let message = `体脂数据趋势分析（最近${bodyData.length}条记录）：\n`;

    bodyData.forEach((data, index) => {
      const date = new Date(data.measuredAt).toLocaleDateString();
      message += `${date}: 体重${data.weight}kg`;
      if (data.bodyFat) message += `, 体脂率${data.bodyFat}%`;
      if (data.muscleMass) message += `, 肌肉量${data.muscleMass}kg`;
      message += '\n';
    });

    // 计算变化趋势
    if (bodyData.length >= 2) {
      const latest = bodyData[0];
      const earliest = bodyData[bodyData.length - 1];
      const weightChange = latest.weight - earliest.weight;
      const bodyFatChange = latest.bodyFat && earliest.bodyFat ? 
        latest.bodyFat - earliest.bodyFat : null;

      message += `\n变化趋势：
- 体重变化: ${weightChange > 0 ? '+' : ''}${weightChange.toFixed(1)}kg`;
      
      if (bodyFatChange !== null) {
        message += `\n- 体脂率变化: ${bodyFatChange > 0 ? '+' : ''}${bodyFatChange.toFixed(1)}%`;
      }
    }

    return message;
  }
}