import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface VerificationCode {
  code: string;
  type: 'register' | 'reset_password' | 'login';
  expiresAt: Date;
  attempts: number;
}

@Injectable()
export class VerificationCodeService {
  private codes = new Map<string, VerificationCode>();
  private readonly maxAttempts = 3;
  private readonly codeLength = 6;
  private readonly expirationMinutes = 5;

  constructor(private configService: ConfigService) {}

  /**
   * 生成验证码
   */
  generateCode(): string {
    return Math.random().toString().slice(2, 2 + this.codeLength);
  }

  /**
   * 存储验证码
   */
  storeCode(identifier: string, type: string): string {
    const code = this.generateCode();
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + this.expirationMinutes);

    this.codes.set(identifier, {
      code,
      type: type as any,
      expiresAt,
      attempts: 0,
    });

    return code;
  }

  /**
   * 验证验证码
   */
  verify(identifier: string, inputCode: string, type: string): boolean {
    const storedCode = this.codes.get(identifier);

    if (!storedCode) {
      return false;
    }

    // 检查类型是否匹配
    if (storedCode.type !== type) {
      return false;
    }

    // 检查是否过期
    if (new Date() > storedCode.expiresAt) {
      this.codes.delete(identifier);
      return false;
    }

    // 检查尝试次数
    if (storedCode.attempts >= this.maxAttempts) {
      this.codes.delete(identifier);
      return false;
    }

    // 验证码错误，增加尝试次数
    if (storedCode.code !== inputCode) {
      storedCode.attempts++;
      return false;
    }

    // 验证成功，删除验证码
    this.codes.delete(identifier);
    return true;
  }

  /**
   * 清理过期验证码
   */
  cleanupExpiredCodes(): void {
    const now = new Date();
    for (const [identifier, codeData] of this.codes.entries()) {
      if (now > codeData.expiresAt) {
        this.codes.delete(identifier);
      }
    }
  }

  /**
   * 检查验证码发送频率限制
   */
  canSendCode(identifier: string): boolean {
    const storedCode = this.codes.get(identifier);
    
    if (!storedCode) {
      return true;
    }

    // 如果验证码还有效，且创建时间不超过1分钟，则不能重新发送
    const oneMinuteAgo = new Date();
    oneMinuteAgo.setMinutes(oneMinuteAgo.getMinutes() - 1);
    
    const codeCreatedAt = new Date(storedCode.expiresAt.getTime() - this.expirationMinutes * 60 * 1000);
    
    return codeCreatedAt < oneMinuteAgo;
  }
}