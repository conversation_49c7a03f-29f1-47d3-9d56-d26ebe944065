import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getHealth() {
    return {
      status: 'ok',
      message: 'HealthyLife API服务器运行正常',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }

  getInfo() {
    return {
      name: 'HealthyLife API',
      version: '1.0.0',
      description: '智能健康管理应用后端API服务',
      environment: process.env.NODE_ENV || 'development',
      nodeVersion: process.version,
    };
  }
}