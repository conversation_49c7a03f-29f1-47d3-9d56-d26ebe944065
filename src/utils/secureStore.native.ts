/*
  secureStore.native.ts - Native platform secure storage implementation
  - Optimized implementation for iOS and Android using expo-secure-store
  - Provides enhanced security features available only on native platforms
  - Maintains backward compatibility while offering extended functionality
  - Implements strict security policies with no fallback to insecure storage
*/

import * as SecureStore from 'expo-secure-store';

// Storage interface for type safety
interface ISecureStorage {
  setItem(key: string, value: string): Promise<void>;
  getItem(key: string): Promise<string | null>;
  removeItem(key: string): Promise<void>;
  isAvailable(): Promise<boolean>;
}

// Enhanced native storage with additional security options
class EnhancedNativeStorage implements ISecureStorage {
  async isAvailable(): Promise<boolean> {
    try {
      return await SecureStore.isAvailableAsync();
    } catch {
      return false;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    const available = await this.isAvailable();
    if (!available) {
      throw new Error('SecureStore is not available on this device');
    }

    // Use enhanced security options for native platforms
    await SecureStore.setItemAsync(key, value, {
      requireAuthentication: false, // Can be configured based on security requirements
      keychainService: 'HealthyLifeSecureStorage',
      touchID: false, // Can be enabled for additional security
    });
  }

  async getItem(key: string): Promise<string | null> {
    const available = await this.isAvailable();
    if (!available) {
      return null;
    }

    return await SecureStore.getItemAsync(key, {
      keychainService: 'HealthyLifeSecureStorage',
    });
  }

  async removeItem(key: string): Promise<void> {
    const available = await this.isAvailable();
    if (!available) {
      return;
    }

    await SecureStore.deleteItemAsync(key, {
      keychainService: 'HealthyLifeSecureStorage',
    });
  }
}

// Singleton storage instance
const storage = new EnhancedNativeStorage();

// Public API - Generic storage functions
export async function setItem(key: string, value: string): Promise<void> {
  try {
    await storage.setItem(key, value);
  } catch (error) {
    console.error(`Failed to store item with key "${key}" securely:`, error);
    throw error; // Re-throw for native platforms to maintain strict security
  }
}

export async function getItem(key: string): Promise<string | null> {
  try {
    return await storage.getItem(key);
  } catch (error) {
    console.error(`Failed to retrieve item with key "${key}" securely:`, error);
    return null;
  }
}

export async function removeItem(key: string): Promise<void> {
  try {
    await storage.removeItem(key);
  } catch (error) {
    console.error(`Failed to remove item with key "${key}" securely:`, error);
  }
}

export async function isStorageAvailable(): Promise<boolean> {
  return await storage.isAvailable();
}

// Legacy token-specific API for backward compatibility
const TOKEN_KEY = 'authToken';

export async function saveToken(token: string): Promise<void> {
  await setItem(TOKEN_KEY, token);
}

export async function getToken(): Promise<string | null> {
  return await getItem(TOKEN_KEY);
}

export async function deleteToken(): Promise<void> {
  await removeItem(TOKEN_KEY);
}

// Default export for backward compatibility
export default { saveToken, getToken, deleteToken, setItem, getItem, removeItem, isStorageAvailable };
