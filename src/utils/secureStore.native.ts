/*
  secureStore.native.ts
  - NATIVE-ONLY implementation for secure token storage.
  - This file provides a robust and secure way to store authentication tokens on iOS and Android
    by exclusively using `expo-secure-store`.
  - It avoids fallbacks to insecure storage like AsyncStorage, ensuring that if SecureStore fails,
    the error is logged explicitly, preventing silent security downgrades.
*/

import * as SecureStore from 'expo-secure-store';

const TOKEN_KEY = 'authToken';

/**
 * Saves the authentication token securely.
 * This function is for NATIVE platforms only.
 * @param token The authentication token to save.
 */
export async function saveToken(token: string): Promise<void> {
  try {
    // Check if the device supports SecureStore before attempting to write.
    const isAvailable = await SecureStore.isAvailableAsync();
    if (!isAvailable) {
      console.error('SecureStore is not available on this device.');
      // In a real-world app, you might want to throw an error or handle this case more gracefully.
      return;
    }
    await SecureStore.setItemAsync(TOKEN_KEY, token);
  } catch (error) {
    console.error('Failed to save token securely.', error);
    // Depending on the app's security requirements, you might want to re-throw the error
    // or notify the user.
  }
}

/**
 * Retrieves the authentication token securely.
 * This function is for NATIVE platforms only.
 * @returns A promise that resolves to the stored token, or `null` if it doesn't exist.
 */
export async function getToken(): Promise<string | null> {
  try {
    const isAvailable = await SecureStore.isAvailableAsync();
    if (!isAvailable) {
      console.error('SecureStore is not available on this device.');
      return null;
    }
    return await SecureStore.getItemAsync(TOKEN_KEY);
  } catch (error) {
    console.error('Failed to get token securely.', error);
    return null;
  }
}

/**
 * Deletes the authentication token securely.
 * This function is for NATIVE platforms only.
 */
export async function deleteToken(): Promise<void> {
  try {
    const isAvailable = await SecureStore.isAvailableAsync();
    if (!isAvailable) {
      console.error('SecureStore is not available on this device.');
      return;
    }
    await SecureStore.deleteItemAsync(TOKEN_KEY);
  } catch (error) {
    console.error('Failed to delete token securely.', error);
  }
}

export default { saveToken, getToken, deleteToken };
