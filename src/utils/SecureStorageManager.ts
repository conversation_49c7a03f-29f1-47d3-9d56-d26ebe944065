/*
  SecureStorageManager.ts - Enhanced storage manager with advanced features
  - Provides high-level storage operations with automatic platform detection
  - Implements type-safe storage with serialization/deserialization
  - Includes error handling, logging, and security features
  - Supports storage events and migration capabilities
*/

import { Platform } from 'react-native';
import {
  IStorageAdapter,
  StorageError,
  StorageErrorCode,
  StorageEvent,
  StorageEventListener,
  SecurityLevel,
  Platform as PlatformType,
  TypedStorage,
  STORAGE_KEYS
} from './storage.types';
import * as secureStore from './secureStore';

class SecureStorageManager implements TypedStorage {
  private listeners: StorageEventListener[] = [];
  private platform: PlatformType;

  constructor() {
    this.platform = this.detectPlatform();
  }

  private detectPlatform(): PlatformType {
    switch (Platform.OS) {
      case 'ios':
        return 'ios';
      case 'android':
        return 'android';
      case 'web':
        return 'web';
      default:
        return 'unknown';
    }
  }

  private emitEvent(event: Omit<StorageEvent, 'timestamp' | 'platform'>): void {
    const fullEvent: StorageEvent = {
      ...event,
      timestamp: new Date(),
      platform: this.platform,
    };
    
    this.listeners.forEach(listener => {
      try {
        listener(fullEvent);
      } catch (error) {
        console.warn('Storage event listener error:', error);
      }
    });
  }

  // Event management
  addEventListener(listener: StorageEventListener): void {
    this.listeners.push(listener);
  }

  removeEventListener(listener: StorageEventListener): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // Core storage operations with event emission
  async setString(key: string, value: string): Promise<void> {
    try {
      await secureStore.setItem(key, value);
      this.emitEvent({ type: 'set', key, success: true });
    } catch (error) {
      const storageError = error instanceof StorageError 
        ? error 
        : new StorageError(`Failed to set string for key: ${key}`, StorageErrorCode.UNKNOWN_ERROR, this.platform);
      
      this.emitEvent({ type: 'set', key, success: false, error: storageError });
      throw storageError;
    }
  }

  async getString(key: string): Promise<string | null> {
    try {
      const value = await secureStore.getItem(key);
      this.emitEvent({ type: 'get', key, success: true });
      return value;
    } catch (error) {
      const storageError = error instanceof StorageError 
        ? error 
        : new StorageError(`Failed to get string for key: ${key}`, StorageErrorCode.UNKNOWN_ERROR, this.platform);
      
      this.emitEvent({ type: 'get', key, success: false, error: storageError });
      return null;
    }
  }

  async setObject<T>(key: string, value: T): Promise<void> {
    try {
      const serialized = JSON.stringify(value);
      await this.setString(key, serialized);
    } catch (error) {
      throw new StorageError(
        `Failed to serialize and store object for key: ${key}`,
        StorageErrorCode.UNKNOWN_ERROR,
        this.platform
      );
    }
  }

  async getObject<T>(key: string): Promise<T | null> {
    try {
      const serialized = await this.getString(key);
      if (serialized === null) {
        return null;
      }
      return JSON.parse(serialized) as T;
    } catch (error) {
      console.warn(`Failed to deserialize object for key: ${key}`, error);
      return null;
    }
  }

  async setBoolean(key: string, value: boolean): Promise<void> {
    await this.setString(key, value.toString());
  }

  async getBoolean(key: string): Promise<boolean | null> {
    const value = await this.getString(key);
    if (value === null) {
      return null;
    }
    return value === 'true';
  }

  async setNumber(key: string, value: number): Promise<void> {
    await this.setString(key, value.toString());
  }

  async getNumber(key: string): Promise<number | null> {
    const value = await this.getString(key);
    if (value === null) {
      return null;
    }
    const parsed = parseFloat(value);
    return isNaN(parsed) ? null : parsed;
  }

  // Convenience methods for common operations
  async removeItem(key: string): Promise<void> {
    try {
      await secureStore.removeItem(key);
      this.emitEvent({ type: 'remove', key, success: true });
    } catch (error) {
      const storageError = error instanceof StorageError 
        ? error 
        : new StorageError(`Failed to remove item for key: ${key}`, StorageErrorCode.UNKNOWN_ERROR, this.platform);
      
      this.emitEvent({ type: 'remove', key, success: false, error: storageError });
      throw storageError;
    }
  }

  async isAvailable(): Promise<boolean> {
    return await secureStore.isStorageAvailable();
  }

  // Token management (backward compatibility)
  async saveToken(token: string): Promise<void> {
    await this.setString(STORAGE_KEYS.AUTH_TOKEN, token);
  }

  async getToken(): Promise<string | null> {
    return await this.getString(STORAGE_KEYS.AUTH_TOKEN);
  }

  async deleteToken(): Promise<void> {
    await this.removeItem(STORAGE_KEYS.AUTH_TOKEN);
  }

  // Utility methods
  getPlatform(): PlatformType {
    return this.platform;
  }

  getSecurityLevel(): SecurityLevel {
    switch (this.platform) {
      case 'ios':
      case 'android':
        return SecurityLevel.HIGH;
      case 'web':
        return SecurityLevel.LOW;
      default:
        return SecurityLevel.MEDIUM;
    }
  }

  // Batch operations
  async setMultiple(items: Record<string, string>): Promise<void> {
    const promises = Object.entries(items).map(([key, value]) => 
      this.setString(key, value)
    );
    await Promise.all(promises);
  }

  async getMultiple(keys: string[]): Promise<Record<string, string | null>> {
    const promises = keys.map(async key => ({
      key,
      value: await this.getString(key)
    }));
    
    const results = await Promise.all(promises);
    return results.reduce((acc, { key, value }) => {
      acc[key] = value;
      return acc;
    }, {} as Record<string, string | null>);
  }
}

// Singleton instance
export const storageManager = new SecureStorageManager();

// Export for direct usage
export default storageManager;
