/*
  storage/index.ts - Main entry point for secure storage system
*/

import { Platform } from 'react-native';
import { IStorageAdapter, Platform as PlatformType, STORAGE_KEYS } from './types';
import { NativeAdapter } from './adapters/NativeAdapter';
import { WebAdapter } from './adapters/WebAdapter';

// Storage factory
function createStorageAdapter(): IStorageAdapter {
  const platform = detectPlatform();
  
  if (platform === 'web') {
    return new WebAdapter();
  }
  
  return new NativeAdapter(platform);
}

function detectPlatform(): PlatformType {
  switch (Platform.OS) {
    case 'ios':
      return 'ios';
    case 'android':
      return 'android';
    case 'web':
      return 'web';
    default:
      return 'unknown';
  }
}

// Singleton storage instance
const storage = createStorageAdapter();

// Public API - Generic storage functions
export async function setItem(key: string, value: string): Promise<void> {
  try {
    await storage.setItem(key, value);
  } catch (error) {
    console.error(`Failed to store item with key "${key}":`, error);
    throw error;
  }
}

export async function getItem(key: string): Promise<string | null> {
  try {
    return await storage.getItem(key);
  } catch (error) {
    console.error(`Failed to retrieve item with key "${key}":`, error);
    return null;
  }
}

export async function removeItem(key: string): Promise<void> {
  try {
    await storage.removeItem(key);
  } catch (error) {
    console.error(`Failed to remove item with key "${key}":`, error);
  }
}

export async function isStorageAvailable(): Promise<boolean> {
  return await storage.isAvailable();
}

// Legacy token-specific API for backward compatibility
export async function saveToken(token: string): Promise<void> {
  await setItem(STORAGE_KEYS.AUTH_TOKEN, token);
}

export async function getToken(): Promise<string | null> {
  return await getItem(STORAGE_KEYS.AUTH_TOKEN);
}

export async function deleteToken(): Promise<void> {
  await removeItem(STORAGE_KEYS.AUTH_TOKEN);
}

// Utility functions
export function getPlatform(): PlatformType {
  return storage.platform;
}

export function getSecurityLevel() {
  return storage.securityLevel;
}

// Default export for backward compatibility
export default { 
  saveToken, 
  getToken, 
  deleteToken, 
  setItem, 
  getItem, 
  removeItem, 
  isStorageAvailable,
  getPlatform,
  getSecurityLevel
};
