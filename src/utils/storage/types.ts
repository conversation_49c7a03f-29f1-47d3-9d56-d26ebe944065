/*
  storage/types.ts - Core interface definitions for secure storage system
*/

// Core storage interface
export interface ISecureStorage {
  setItem(key: string, value: string): Promise<void>;
  getItem(key: string): Promise<string | null>;
  removeItem(key: string): Promise<void>;
  isAvailable(): Promise<boolean>;
}

// Platform types
export type Platform = 'ios' | 'android' | 'web' | 'unknown';

// Security levels
export enum SecurityLevel {
  LOW = 'low',
  MEDIUM = 'medium', 
  HIGH = 'high'
}

// Storage error types
export class StorageError extends Error {
  constructor(
    message: string,
    public code: StorageErrorCode,
    public platform: Platform
  ) {
    super(message);
    this.name = 'StorageError';
  }
}

export enum StorageErrorCode {
  NOT_AVAILABLE = 'NOT_AVAILABLE',
  ITEM_NOT_FOUND = 'ITEM_NOT_FOUND',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// Storage adapter interface
export interface IStorageAdapter {
  readonly platform: Platform;
  readonly securityLevel: SecurityLevel;
  setItem(key: string, value: string): Promise<void>;
  getItem(key: string): Promise<string | null>;
  removeItem(key: string): Promise<void>;
  isAvailable(): Promise<boolean>;
}

// Common storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'authToken',
  REFRESH_TOKEN: 'refreshToken',
  USER_PREFERENCES: 'userPreferences',
  DEVICE_ID: 'deviceId',
} as const;

export type StorageKey = typeof STORAGE_KEYS[keyof typeof STORAGE_KEYS];
