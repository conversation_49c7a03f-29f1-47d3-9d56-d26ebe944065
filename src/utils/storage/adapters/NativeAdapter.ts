/*
  storage/adapters/NativeAdapter.ts - Native platform storage adapter
*/

import * as SecureStore from 'expo-secure-store';
import { IStorageAdapter, Platform, SecurityLevel, StorageError, StorageErrorCode } from '../types';

export class NativeAdapter implements IStorageAdapter {
  readonly platform: Platform;
  readonly securityLevel = SecurityLevel.HIGH;

  constructor(platform: Platform) {
    this.platform = platform;
  }

  async isAvailable(): Promise<boolean> {
    try {
      return await SecureStore.isAvailableAsync();
    } catch {
      return false;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    const available = await this.isAvailable();
    if (!available) {
      throw new StorageError('SecureStore is not available', StorageErrorCode.NOT_AVAILABLE, this.platform);
    }
    
    try {
      await SecureStore.setItemAsync(key, value, {
        keychainService: 'HealthyLifeSecureStorage',
      });
    } catch (error) {
      throw new StorageError(
        `Failed to store item: ${error}`,
        StorageErrorCode.UNKNOWN_ERROR,
        this.platform
      );
    }
  }

  async getItem(key: string): Promise<string | null> {
    const available = await this.isAvailable();
    if (!available) {
      return null;
    }
    
    try {
      return await SecureStore.getItemAsync(key, {
        keychainService: 'HealthyLifeSecureStorage',
      });
    } catch (error) {
      console.error(`Failed to retrieve item with key "${key}":`, error);
      return null;
    }
  }

  async removeItem(key: string): Promise<void> {
    const available = await this.isAvailable();
    if (!available) {
      return;
    }
    
    try {
      await SecureStore.deleteItemAsync(key, {
        keychainService: 'HealthyLifeSecureStorage',
      });
    } catch (error) {
      console.error(`Failed to remove item with key "${key}":`, error);
    }
  }
}
