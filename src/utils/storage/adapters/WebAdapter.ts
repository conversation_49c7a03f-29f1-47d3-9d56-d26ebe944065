/*
  storage/adapters/WebAdapter.ts - Web platform storage adapter
*/

import AsyncStorage from '@react-native-async-storage/async-storage';
import { IStorageAdapter, Platform, SecurityLevel, StorageError, StorageErrorCode } from '../types';

export class WebAdapter implements IStorageAdapter {
  readonly platform: Platform = 'web';
  readonly securityLevel = SecurityLevel.LOW;
  private hasWarned = false;

  async isAvailable(): Promise<boolean> {
    return true; // AsyncStorage is always available
  }

  private warnOnce(operation: string): void {
    if (!this.hasWarned) {
      console.warn(
        `[SECURITY WARNING] Using insecure storage (AsyncStorage) for ${operation}. ` +
        'This is only suitable for development. Consider implementing proper web encryption for production.'
      );
      this.hasWarned = true;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    this.warnOnce('storing sensitive data');
    try {
      await AsyncStorage.setItem(key, value);
    } catch (error) {
      throw new StorageError(
        `Failed to store item: ${error}`,
        StorageErrorCode.UNKNOWN_ERROR,
        this.platform
      );
    }
  }

  async getItem(key: string): Promise<string | null> {
    this.warnOnce('retrieving sensitive data');
    try {
      return await AsyncStorage.getItem(key);
    } catch (error) {
      console.error(`Failed to retrieve item with key "${key}":`, error);
      return null;
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error(`Failed to remove item with key "${key}":`, error);
    }
  }
}
