/*
  secureStore.ts (for web and other non-native platforms)
  - This file provides a mock or fallback implementation for token storage when `expo-secure-store`
    is not available, such as in a web environment.
  - It uses `AsyncStorage` as a substitute, which is NOT secure. This implementation is intended
    for development and testing purposes on non-native platforms only.
  - It ensures that the app can run without crashing, but it logs warnings to the console
    to remind developers that the storage is not secure.
*/

import AsyncStorage from '@react-native-async-storage/async-storage';

const TOKEN_KEY = 'authToken';

/**
 * Saves the authentication token using AsyncStorage (INSECURE).
 * This is a fallback for non-native platforms.
 * @param token The authentication token to save.
 */
export async function saveToken(token: string): Promise<void> {
  try {
    console.warn('Using insecure storage (AsyncStorage) for token. For development only.');
    await AsyncStorage.setItem(TOKEN_KEY, token);
  } catch (error) {
    console.error('Failed to save token to AsyncStorage.', error);
  }
}

/**
 * Retrieves the authentication token from AsyncStorage (INSECURE).
 * This is a fallback for non-native platforms.
 * @returns A promise that resolves to the stored token, or `null` if it doesn't exist.
 */
export async function getToken(): Promise<string | null> {
  try {
    console.warn('Retrieving token from insecure storage (AsyncStorage). For development only.');
    return await AsyncStorage.getItem(TOKEN_KEY);
  } catch (error) {
    console.error('Failed to get token from AsyncStorage.', error);
    return null;
  }
}

/**
 * Deletes the authentication token from AsyncStorage (INSECURE).
 * This is a fallback for non-native platforms.
 */
export async function deleteToken(): Promise<void> {
  try {
    await AsyncStorage.removeItem(TOKEN_KEY);
  } catch (error) {
    console.error('Failed to delete token from AsyncStorage.', error);
  }
}

export default { saveToken, getToken, deleteToken };

