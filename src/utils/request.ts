import { store } from '../store';

export async function request(input: RequestInfo, init?: RequestInit) {
  const state = store.getState();
  const token = state.auth?.token;

  // Debug logging (only in development)
  if (__DEV__) {
    console.log('Request debug:', {
      url: input,
      hasToken: !!token,
      tokenPreview: token ? `${token.substring(0, 10)}...` : 'none',
      isAuthenticated: state.auth?.isAuthenticated
    });
  }

  const baseHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(init && init.headers ? (init.headers as Record<string, string>) : {}),
  };

  const headers = token
    ? { ...baseHeaders, Authorization: `Bearer ${token}` }
    : baseHeaders;

  const res = await fetch(input, { ...init, headers });

  // Debug response (only in development)
  if (__DEV__ && !res.ok) {
    console.log('Request failed:', {
      status: res.status,
      statusText: res.statusText,
      url: input
    });
  }

  return res;
}
