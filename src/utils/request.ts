import { store } from '../store';

export async function request(input: RequestInfo, init?: RequestInit) {
  const state = store.getState();
  const token = state.auth?.token;

  const baseHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(init && init.headers ? (init.headers as Record<string, string>) : {}),
  };

  const headers = token
    ? { ...baseHeaders, Authorization: `Bearer ${token}` }
    : baseHeaders;

  const res = await fetch(input, { ...init, headers });
  return res;
}
