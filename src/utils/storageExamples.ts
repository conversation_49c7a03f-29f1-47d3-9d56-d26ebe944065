/*
  storageExamples.ts - Usage examples for the enhanced storage system
  - Demonstrates various storage operations and patterns
  - Shows how to use type-safe storage methods
  - Provides examples for error handling and event monitoring
  - NOT FOR PRODUCTION USE - This is for reference only
*/

import storageManager from './SecureStorageManager';
import { StorageEvent, STORAGE_KEYS } from './storage.types';

// Example 1: Basic string storage
export async function basicStringExample() {
  try {
    // Store a string value
    await storageManager.setString('user_name', '<PERSON>');
    
    // Retrieve the string value
    const userName = await storageManager.getString('user_name');
    console.log('Retrieved user name:', userName);
    
    // Remove the value
    await storageManager.removeItem('user_name');
  } catch (error) {
    console.error('String storage error:', error);
  }
}

// Example 2: Object storage with type safety
interface UserPreferences {
  theme: 'light' | 'dark';
  language: 'en' | 'zh';
  notifications: boolean;
}

export async function objectStorageExample() {
  try {
    const preferences: UserPreferences = {
      theme: 'dark',
      language: 'en',
      notifications: true
    };
    
    // Store typed object
    await storageManager.setObject(STORAGE_KEYS.USER_PREFERENCES, preferences);
    
    // Retrieve typed object
    const savedPreferences = await storageManager.getObject<UserPreferences>(
      STORAGE_KEYS.USER_PREFERENCES
    );
    
    if (savedPreferences) {
      console.log('User preferences:', savedPreferences);
      console.log('Theme:', savedPreferences.theme); // TypeScript knows this is 'light' | 'dark'
    }
  } catch (error) {
    console.error('Object storage error:', error);
  }
}

// Example 3: Boolean and number storage
export async function primitiveTypesExample() {
  try {
    // Boolean storage
    await storageManager.setBoolean('biometric_enabled', true);
    const biometricEnabled = await storageManager.getBoolean('biometric_enabled');
    console.log('Biometric enabled:', biometricEnabled);
    
    // Number storage
    await storageManager.setNumber('user_height', 175.5);
    const userHeight = await storageManager.getNumber('user_height');
    console.log('User height:', userHeight);
  } catch (error) {
    console.error('Primitive types storage error:', error);
  }
}

// Example 4: Batch operations
export async function batchOperationsExample() {
  try {
    // Set multiple values at once
    await storageManager.setMultiple({
      'setting_1': 'value1',
      'setting_2': 'value2',
      'setting_3': 'value3'
    });
    
    // Get multiple values at once
    const values = await storageManager.getMultiple([
      'setting_1',
      'setting_2',
      'setting_3',
      'non_existent_key'
    ]);
    
    console.log('Batch retrieved values:', values);
    // Output: { setting_1: 'value1', setting_2: 'value2', setting_3: 'value3', non_existent_key: null }
  } catch (error) {
    console.error('Batch operations error:', error);
  }
}

// Example 5: Event monitoring
export function eventMonitoringExample() {
  // Add event listener to monitor storage operations
  const listener = (event: StorageEvent) => {
    console.log(`Storage ${event.type} operation:`, {
      key: event.key,
      success: event.success,
      timestamp: event.timestamp,
      platform: event.platform,
      error: event.error?.message
    });
  };
  
  storageManager.addEventListener(listener);
  
  // Perform some operations that will trigger events
  storageManager.setString('test_key', 'test_value');
  storageManager.getString('test_key');
  storageManager.removeItem('test_key');
  
  // Remove listener when done
  // storageManager.removeEventListener(listener);
}

// Example 6: Platform-aware storage
export async function platformAwareExample() {
  const platform = storageManager.getPlatform();
  const securityLevel = storageManager.getSecurityLevel();
  
  console.log(`Running on ${platform} with ${securityLevel} security level`);
  
  // Adjust behavior based on platform
  if (platform === 'web') {
    console.warn('Running on web - storage is not fully secure');
    // Maybe show a warning to the user
  } else {
    console.log('Running on native platform - storage is secure');
  }
  
  // Check if storage is available
  const isAvailable = await storageManager.isAvailable();
  if (!isAvailable) {
    console.error('Secure storage is not available on this device');
    // Handle fallback or show error to user
  }
}

// Example 7: Token management (backward compatibility)
export async function tokenManagementExample() {
  try {
    // Save authentication token
    await storageManager.saveToken('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...');
    
    // Retrieve token
    const token = await storageManager.getToken();
    if (token) {
      console.log('Token retrieved successfully');
      // Use token for API calls
    }
    
    // Delete token (logout)
    await storageManager.deleteToken();
    console.log('Token deleted');
  } catch (error) {
    console.error('Token management error:', error);
  }
}

// Example 8: Error handling patterns
export async function errorHandlingExample() {
  try {
    await storageManager.setString('test_key', 'test_value');
  } catch (error) {
    if (error instanceof Error) {
      console.error('Storage operation failed:', error.message);
      
      // Handle specific error types
      if (error.message.includes('not available')) {
        // Storage not available - maybe show offline mode
        console.log('Switching to offline mode');
      } else if (error.message.includes('permission')) {
        // Permission denied - maybe request permissions
        console.log('Requesting storage permissions');
      }
    }
  }
}

// Example usage function
export async function runAllExamples() {
  console.log('=== Storage Examples ===');
  
  await basicStringExample();
  await objectStorageExample();
  await primitiveTypesExample();
  await batchOperationsExample();
  eventMonitoringExample();
  await platformAwareExample();
  await tokenManagementExample();
  await errorHandlingExample();
  
  console.log('=== Examples Complete ===');
}
