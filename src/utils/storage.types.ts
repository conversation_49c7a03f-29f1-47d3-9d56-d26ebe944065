/*
  storage.types.ts - Type definitions for secure storage system
  - Provides comprehensive TypeScript interfaces for storage operations
  - Defines error types and security levels
  - Ensures type safety across all storage implementations
*/

// Core storage interface
export interface ISecureStorage {
  setItem(key: string, value: string): Promise<void>;
  getItem(key: string): Promise<string | null>;
  removeItem(key: string): Promise<void>;
  isAvailable(): Promise<boolean>;
}

// Storage configuration options
export interface StorageOptions {
  requireAuthentication?: boolean;
  keychainService?: string;
  touchID?: boolean;
  showModal?: boolean;
  promptMessage?: string;
}

// Platform types
export type Platform = 'ios' | 'android' | 'web' | 'unknown';

// Security levels
export enum SecurityLevel {
  LOW = 'low',           // Basic storage, minimal security
  MEDIUM = 'medium',     // Standard secure storage
  HIGH = 'high',         // Enhanced security with biometrics
  CRITICAL = 'critical'  // Maximum security, requires authentication
}

// Storage error types
export class StorageError extends Error {
  constructor(
    message: string,
    public code: StorageErrorCode,
    public platform: Platform
  ) {
    super(message);
    this.name = 'StorageError';
  }
}

export enum StorageErrorCode {
  NOT_AVAILABLE = 'NOT_AVAILABLE',
  AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
  ITEM_NOT_FOUND = 'ITEM_NOT_FOUND',
  STORAGE_FULL = 'STORAGE_FULL',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// Storage adapter interface
export interface IStorageAdapter {
  readonly platform: Platform;
  readonly securityLevel: SecurityLevel;
  setItem(key: string, value: string, options?: StorageOptions): Promise<void>;
  getItem(key: string, options?: StorageOptions): Promise<string | null>;
  removeItem(key: string, options?: StorageOptions): Promise<void>;
  isAvailable(): Promise<boolean>;
  clear(): Promise<void>;
}

// Storage factory interface
export interface IStorageFactory {
  createStorage(platform?: Platform): IStorageAdapter;
  getSupportedPlatforms(): Platform[];
  getRecommendedSecurityLevel(platform: Platform): SecurityLevel;
}

// Common storage keys (can be extended)
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'authToken',
  REFRESH_TOKEN: 'refreshToken',
  USER_PREFERENCES: 'userPreferences',
  DEVICE_ID: 'deviceId',
  BIOMETRIC_ENABLED: 'biometricEnabled',
} as const;

export type StorageKey = typeof STORAGE_KEYS[keyof typeof STORAGE_KEYS];

// Utility types for type-safe storage operations
export interface TypedStorage {
  setString(key: string, value: string): Promise<void>;
  getString(key: string): Promise<string | null>;
  setObject<T>(key: string, value: T): Promise<void>;
  getObject<T>(key: string): Promise<T | null>;
  setBoolean(key: string, value: boolean): Promise<void>;
  getBoolean(key: string): Promise<boolean | null>;
  setNumber(key: string, value: number): Promise<void>;
  getNumber(key: string): Promise<number | null>;
}

// Storage event types for monitoring
export interface StorageEvent {
  type: 'set' | 'get' | 'remove' | 'clear';
  key: string;
  timestamp: Date;
  platform: Platform;
  success: boolean;
  error?: StorageError;
}

export type StorageEventListener = (event: StorageEvent) => void;

// Migration interface for storage upgrades
export interface StorageMigration {
  version: number;
  migrate(storage: IStorageAdapter): Promise<void>;
}
