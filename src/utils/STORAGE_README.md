# Secure Storage System

## 概述

这是一个完全重构的安全存储系统，为HealthyLife应用提供跨平台的安全数据存储功能。系统支持Android、iOS和Web平台，并提供统一的API接口。

## 架构设计

### 核心组件

1. **secureStore.ts** - 通用存储实现，自动平台检测
2. **secureStore.native.ts** - 原生平台优化实现
3. **SecureStorageManager.ts** - 高级存储管理器
4. **storage.types.ts** - 完整的TypeScript类型定义
5. **storageMigration.ts** - 数据迁移工具

### 平台适配

- **iOS/Android**: 使用 `expo-secure-store` 提供硬件级安全存储
- **Web**: 使用 `AsyncStorage` 并显示安全警告
- **自动检测**: 运行时自动选择合适的存储适配器

## 基本使用

### 导入存储模块

```typescript
import * as secureStore from './utils/secureStore';
// 或者使用高级管理器
import storageManager from './utils/SecureStorageManager';
```

### 基本操作

```typescript
// 存储字符串
await secureStore.setItem('key', 'value');

// 获取字符串
const value = await secureStore.getItem('key');

// 删除项目
await secureStore.removeItem('key');

// 检查存储可用性
const isAvailable = await secureStore.isStorageAvailable();
```

### Token管理（向后兼容）

```typescript
// 保存认证token
await secureStore.saveToken('your-jwt-token');

// 获取token
const token = await secureStore.getToken();

// 删除token
await secureStore.deleteToken();
```

## 高级功能

### 类型安全的存储

```typescript
import storageManager from './utils/SecureStorageManager';

// 存储对象
interface UserPrefs {
  theme: 'light' | 'dark';
  language: 'en' | 'zh';
}

const prefs: UserPrefs = { theme: 'dark', language: 'en' };
await storageManager.setObject('user_prefs', prefs);

// 获取类型安全的对象
const savedPrefs = await storageManager.getObject<UserPrefs>('user_prefs');
```

### 基本类型存储

```typescript
// 布尔值
await storageManager.setBoolean('notifications_enabled', true);
const enabled = await storageManager.getBoolean('notifications_enabled');

// 数字
await storageManager.setNumber('user_height', 175.5);
const height = await storageManager.getNumber('user_height');
```

### 批量操作

```typescript
// 批量设置
await storageManager.setMultiple({
  'setting1': 'value1',
  'setting2': 'value2',
  'setting3': 'value3'
});

// 批量获取
const values = await storageManager.getMultiple(['setting1', 'setting2']);
```

### 事件监听

```typescript
import { StorageEvent } from './utils/storage.types';

const listener = (event: StorageEvent) => {
  console.log(`Storage ${event.type}:`, event.key, event.success);
};

storageManager.addEventListener(listener);
// 执行存储操作...
storageManager.removeEventListener(listener);
```

## 安全特性

### 平台安全级别

- **iOS/Android**: HIGH - 硬件安全模块支持
- **Web**: LOW - 浏览器本地存储（开发用）

### 安全配置（原生平台）

```typescript
// 原生平台支持额外的安全选项
// 在 secureStore.native.ts 中配置：
{
  requireAuthentication: false,  // 是否需要生物识别
  keychainService: 'HealthyLifeSecureStorage',
  touchID: false  // 是否启用Touch ID
}
```

## 迁移系统

### 自动迁移

应用启动时会自动检查并执行必要的数据迁移：

```typescript
import { autoMigrate } from './utils/storageMigration';

// 在App.tsx中自动执行
await autoMigrate();
```

### 手动迁移控制

```typescript
import migrationManager from './utils/storageMigration';

// 检查是否需要迁移
const needsMigration = await migrationManager.isMigrationNeeded();

// 执行迁移
const result = await migrationManager.performMigration();

// 回滚迁移
await migrationManager.rollbackMigration();
```

## 错误处理

### 基本错误处理

```typescript
try {
  await secureStore.setItem('key', 'value');
} catch (error) {
  console.error('Storage error:', error);
  // 处理错误...
}
```

### 高级错误处理

```typescript
import { StorageError, StorageErrorCode } from './utils/storage.types';

try {
  await storageManager.setString('key', 'value');
} catch (error) {
  if (error instanceof StorageError) {
    switch (error.code) {
      case StorageErrorCode.NOT_AVAILABLE:
        // 存储不可用
        break;
      case StorageErrorCode.PERMISSION_DENIED:
        // 权限被拒绝
        break;
      default:
        // 其他错误
        break;
    }
  }
}
```

## 最佳实践

1. **使用类型安全的方法**: 优先使用 `storageManager` 的类型安全方法
2. **错误处理**: 始终包装存储操作在try-catch中
3. **平台检测**: 根据平台调整行为和用户提示
4. **事件监听**: 使用事件监听器监控存储操作
5. **批量操作**: 对于多个相关操作，使用批量方法提高性能

## 向后兼容性

新系统完全向后兼容现有代码：

- 所有现有的 `secureStore` 调用继续工作
- Token管理API保持不变
- 现有的导入语句无需修改

## 性能考虑

- 原生平台使用硬件加速的安全存储
- Web平台使用优化的本地存储
- 批量操作减少I/O开销
- 事件系统支持性能监控

## 故障排除

### 常见问题

1. **存储不可用**: 检查设备是否支持安全存储
2. **权限错误**: 确保应用有必要的存储权限
3. **迁移失败**: 检查迁移日志，必要时手动回滚

### 调试

启用存储事件监听器可以帮助调试存储问题：

```typescript
storageManager.addEventListener(event => {
  if (!event.success) {
    console.error('Storage operation failed:', event);
  }
});
```
