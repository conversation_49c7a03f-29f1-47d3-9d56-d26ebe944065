import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { RootState, AppDispatch } from '../store';
import {
  scanForDevices,
  connectToDevice,
  disconnectDevice,
} from '../store/slices/devicesSlice';
import { BluetoothDevice } from '../types';

const DevicesScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { connectedDevices, availableDevices, isScanning, error } = useSelector(
    (state: RootState) => state.devices
  );
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    handleScanDevices();
  }, []);

  const handleScanDevices = async () => {
    try {
      await dispatch(scanForDevices()).unwrap();
    } catch (error) {
      Alert.alert('扫描失败', '请检查蓝牙是否开启');
    }
  };

  const handleConnectDevice = async (deviceId: string) => {
    try {
      await dispatch(connectToDevice(deviceId)).unwrap();
      Alert.alert('连接成功', '设备已成功连接');
    } catch (error) {
      Alert.alert('连接失败', '无法连接到设备，请重试');
    }
  };

  const handleDisconnectDevice = async (deviceId: string) => {
    try {
      await dispatch(disconnectDevice(deviceId)).unwrap();
      Alert.alert('断开连接', '设备已断开连接');
    } catch (error) {
      Alert.alert('断开失败', '无法断开设备连接');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await handleScanDevices();
    setRefreshing(false);
  };

  const renderDeviceItem = ({ item }: { item: BluetoothDevice }) => (
    <View style={styles.deviceItem}>
      <View style={styles.deviceInfo}>
        <View style={styles.deviceHeader}>
          <Ionicons
            name={getDeviceIcon(item.type)}
            size={24}
            color={item.isConnected ? '#10b981' : '#6b7280'}
          />
          <Text style={styles.deviceName}>{item.name}</Text>
          {item.isConnected && (
            <View style={styles.connectedBadge}>
              <Text style={styles.connectedText}>已连接</Text>
            </View>
          )}
        </View>
        <Text style={styles.deviceType}>{getDeviceTypeText(item.type)}</Text>
        {item.lastConnected && (
          <Text style={styles.lastConnected}>
            上次连接: {new Date(item.lastConnected).toLocaleString('zh-CN')}
          </Text>
        )}
      </View>
      <TouchableOpacity
        style={[
          styles.actionButton,
          item.isConnected ? styles.disconnectButton : styles.connectButton,
        ]}
        onPress={() =>
          item.isConnected
            ? handleDisconnectDevice(item.id)
            : handleConnectDevice(item.id)
        }
      >
        <Text
          style={[
            styles.actionButtonText,
            item.isConnected ? styles.disconnectButtonText : styles.connectButtonText,
          ]}
        >
          {item.isConnected ? '断开' : '连接'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  const getDeviceIcon = (type: BluetoothDevice['type']) => {
    switch (type) {
      case 'body_scale':
        return 'scale-outline';
      case 'fitness_tracker':
        return 'watch-outline';
      default:
        return 'bluetooth-outline';
    }
  };

  const getDeviceTypeText = (type: BluetoothDevice['type']) => {
    switch (type) {
      case 'body_scale':
        return '体脂秤';
      case 'fitness_tracker':
        return '健身追踪器';
      default:
        return '其他设备';
    }
  };

  const allDevices = [...connectedDevices, ...availableDevices];

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>设备管理</Text>
        <TouchableOpacity
          style={styles.scanButton}
          onPress={handleScanDevices}
          disabled={isScanning}
        >
          {isScanning ? (
            <ActivityIndicator size="small" color="#6366f1" />
          ) : (
            <Ionicons name="refresh" size={20} color="#6366f1" />
          )}
          <Text style={styles.scanButtonText}>
            {isScanning ? '扫描中...' : '扫描设备'}
          </Text>
        </TouchableOpacity>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Ionicons name="warning" size={20} color="#ef4444" />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {connectedDevices.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>已连接设备</Text>
        </View>
      )}

      {allDevices.length === 0 && !isScanning ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="bluetooth-outline" size={64} color="#9ca3af" />
          <Text style={styles.emptyTitle}>未发现设备</Text>
          <Text style={styles.emptySubtitle}>
            请确保设备已开启并处于配对模式，然后点击扫描设备
          </Text>
        </View>
      ) : (
        <FlatList
          data={allDevices}
          keyExtractor={(item) => item.id}
          renderItem={renderDeviceItem}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  scanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f9ff',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#bfdbfe',
  },
  scanButtonText: {
    marginLeft: 8,
    color: '#6366f1',
    fontWeight: '500',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef2f2',
    padding: 12,
    margin: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  errorText: {
    marginLeft: 8,
    color: '#dc2626',
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
  },
  listContent: {
    padding: 20,
  },
  deviceItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginLeft: 8,
    flex: 1,
  },
  connectedBadge: {
    backgroundColor: '#dcfce7',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  connectedText: {
    fontSize: 12,
    color: '#16a34a',
    fontWeight: '500',
  },
  deviceType: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 32,
  },
  lastConnected: {
    fontSize: 12,
    color: '#9ca3af',
    marginLeft: 32,
    marginTop: 2,
  },
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    alignSelf: 'center',
  },
  connectButton: {
    backgroundColor: '#6366f1',
  },
  disconnectButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ef4444',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  connectButtonText: {
    color: '#fff',
  },
  disconnectButtonText: {
    color: '#ef4444',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default DevicesScreen;