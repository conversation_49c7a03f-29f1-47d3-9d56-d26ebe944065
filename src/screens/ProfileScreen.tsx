import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Switch,
  Platform,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '../types';
import { MaterialIcons } from '@expo/vector-icons';
import { RootState, User } from '../types';
import { setUser } from '../store/slices/authSlice';
import { logout } from '../store/slices/authSlice';
import LanguageSelector from '../components/LanguageSelector';
import { t, getCurrentLanguage, setLanguage as i18nSetLanguage } from '../i18n';
import { setLanguage } from '../store/slices/authSlice';

const ProfileScreen: React.FC = () => {
  const { user, isLoading } = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const [isEditing, setIsEditing] = useState(false);
  const [editedUser, setEditedUser] = useState<Partial<User>>(user || {});
  const [languageSelectorVisible, setLanguageSelectorVisible] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<string>(getCurrentLanguage());

  const handleSave = () => {
    if (editedUser) {
      const updatedUser: User = {
        ...user!,
        ...editedUser,
        preferences: {
          units: editedUser.preferences?.units || user?.preferences?.units || 'metric',
          language: editedUser.preferences?.language || user?.preferences?.language || 'zh',
          notifications: {
            dataReminder: editedUser.preferences?.notifications?.dataReminder ?? 
                         user?.preferences?.notifications?.dataReminder ?? true,
            goalAchievement: editedUser.preferences?.notifications?.goalAchievement ?? 
                           user?.preferences?.notifications?.goalAchievement ?? true,
            suggestions: editedUser.preferences?.notifications?.suggestions ?? 
                        user?.preferences?.notifications?.suggestions ?? true,
          },
        },
      };
      dispatch(setUser(updatedUser));
      setIsEditing(false);
      Alert.alert(t('common.success'), t('profile.updateSuccess'));
    }
  };

  const handleCancel = () => {
    setEditedUser(user || {});
    setIsEditing(false);
  };

  const handleLogout = () => {
    Alert.alert(
      t('profile.confirmLogout'),
      t('profile.logoutConfirm'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.logout'),
          style: 'destructive',
          onPress: () => {
            (async () => {
              try {
                await (dispatch(logout() as any)).unwrap();
                navigation.reset({ index: 0, routes: [{ name: 'Auth' as any }] });
              } catch (err: any) {
                Alert.alert(t('common.error'), err?.message || '登出失败');
              }
            })();
          },
        },
      ]
    );
  };

  const calculateAge = (birthday?: Date) => {
    if (!birthday) return null;
    const today = new Date();
    const birthDate = new Date(birthday);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const calculateBMI = () => {
    if (!user?.height || !editedUser.targetWeight) return null;
    const heightInMeters = user.height / 100;
    return (editedUser.targetWeight / (heightInMeters * heightInMeters)).toFixed(1);
  };

  const getActivityLevelText = (level?: string) => {
    const levels = {
      sedentary: t('profile.sedentary'),
      lightly_active: t('profile.lightlyActive'),
      moderately_active: t('profile.moderatelyActive'),
      very_active: t('profile.veryActive'),
      extremely_active: t('profile.extremelyActive'),
    };
    return levels[level as keyof typeof levels] || t('profile.notSet');
  };

  if (!user) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{t('common.error')}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* 头像和基本信息 */}
      <View style={styles.headerSection}>
        <View style={styles.avatarContainer}>
          <View style={styles.avatar}>
            <MaterialIcons name="person" size={50} color="#64748b" />
          </View>
          {isEditing && (
            <TouchableOpacity style={styles.changeAvatarButton}>
              <MaterialIcons name="camera-alt" size={20} color="#fff" />
            </TouchableOpacity>
          )}
        </View>
        
        <View style={styles.userInfo}>
          {isEditing ? (
            <TextInput
              style={styles.nameInput}
              value={editedUser.name || ''}
              onChangeText={(text) => setEditedUser({ ...editedUser, name: text })}
              placeholder={t('profile.enterName')}
            />
          ) : (
            <Text style={styles.userName}>{user.name}</Text>
          )}
          <Text style={styles.userEmail}>{user.email}</Text>
          <View style={styles.providerBadge}>
            <Text style={styles.providerText}>
              {user.authProvider === 'google' && t('profile.googleLogin')}
              {user.authProvider === 'apple' && t('profile.appleLogin')}
              {user.authProvider === 'email' && t('profile.emailLogin')}
              {user.authProvider === 'phone' && t('profile.phoneLogin')}
            </Text>
          </View>
        </View>
        
        <TouchableOpacity
          style={styles.editButton}
          onPress={() => (isEditing ? handleSave() : setIsEditing(true))}
        >
          <MaterialIcons
            name={isEditing ? 'check' : 'edit'}
            size={20}
            color="#3b82f6"
          />
        </TouchableOpacity>
        
        {isEditing && (
          <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
            <MaterialIcons name="close" size={20} color="#ef4444" />
          </TouchableOpacity>
        )}
      </View>

      {/* 个人资料 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('profile.personalInfo')}</Text>
        
        {/* 性别 */}
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>{t('profile.gender')}</Text>
          {isEditing ? (
            <View style={styles.genderSelector}>
              {['male', 'female', 'other'].map((gender) => (
                <TouchableOpacity
                  key={gender}
                  style={[
                    styles.genderOption,
                    editedUser.gender === gender && styles.genderOptionSelected,
                  ]}
                  onPress={() => setEditedUser({ ...editedUser, gender: gender as any })}
                >
                  <Text
                    style={[
                      styles.genderOptionText,
                      editedUser.gender === gender && styles.genderOptionTextSelected,
                    ]}
                  >
                    {gender === 'male' && t('common.male')}
                    {gender === 'female' && t('common.female')}
                    {gender === 'other' && t('common.other')}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <Text style={styles.infoValue}>
              {user.gender === 'male' && t('common.male')}
              {user.gender === 'female' && t('common.female')}
              {user.gender === 'other' && t('common.other')}
              {!user.gender && t('profile.notSet')}
            </Text>
          )}
        </View>

        {/* 年龄 */}
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>{t('profile.age')}</Text>
          <Text style={styles.infoValue}>
            {calculateAge(user.birthday) ? `${calculateAge(user.birthday)}${t('profile.years')}` : t('profile.notSet')}
          </Text>
        </View>

        {/* 身高 */}
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>{t('profile.height')}</Text>
          {isEditing ? (
            <TextInput
              style={styles.infoInput}
              value={editedUser.height?.toString() || ''}
              onChangeText={(text) =>
                setEditedUser({ ...editedUser, height: parseFloat(text) || undefined })
              }
              placeholder={`${t('profile.height')} (${t('profile.cm')})`}
              keyboardType="numeric"
            />
          ) : (
            <Text style={styles.infoValue}>
              {user.height ? `${user.height} ${t('profile.cm')}` : t('profile.notSet')}
            </Text>
          )}
        </View>

        {/* 目标体重 */}
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>{t('profile.targetWeight')}</Text>
          {isEditing ? (
            <TextInput
              style={styles.infoInput}
              value={editedUser.targetWeight?.toString() || ''}
              onChangeText={(text) =>
                setEditedUser({ ...editedUser, targetWeight: parseFloat(text) || undefined })
              }
              placeholder={`${t('profile.targetWeight')} (${t('home.kg')})`}
              keyboardType="numeric"
            />
          ) : (
            <Text style={styles.infoValue}>
              {user.targetWeight ? `${user.targetWeight} ${t('home.kg')}` : t('profile.notSet')}
            </Text>
          )}
        </View>

        {/* BMI */}
        {user.height && editedUser.targetWeight && (
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>{t('profile.bmi')}</Text>
            <Text style={styles.infoValue}>{calculateBMI()}</Text>
          </View>
        )}

        {/* 活动水平 */}
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>{t('profile.activityLevel')}</Text>
          {isEditing ? (
            <View style={styles.activitySelector}>
              {[
                'sedentary',
                'lightly_active',
                'moderately_active',
                'very_active',
                'extremely_active',
              ].map((level) => (
                <TouchableOpacity
                  key={level}
                  style={[
                    styles.activityOption,
                    editedUser.activityLevel === level && styles.activityOptionSelected,
                  ]}
                  onPress={() => setEditedUser({ ...editedUser, activityLevel: level as any })}
                >
                  <Text
                    style={[
                      styles.activityOptionText,
                      editedUser.activityLevel === level && styles.activityOptionTextSelected,
                    ]}
                  >
                    {getActivityLevelText(level)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <Text style={styles.infoValue}>{getActivityLevelText(user.activityLevel)}</Text>
          )}
        </View>
      </View>

      {/* 偏好设置 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('profile.preferences')}</Text>

        
        
        {/* 单位制 */}
        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>{t('profile.units')}</Text>
          <View style={styles.unitSelector}>
            <TouchableOpacity
              style={[
                styles.unitOption,
                (editedUser.preferences?.units || 'metric') === 'metric' && styles.unitOptionSelected,
              ]}
              onPress={() =>
                setEditedUser({
                  ...editedUser,
                  preferences: {
                    ...editedUser.preferences,
                    units: 'metric',
                    language: editedUser.preferences?.language || 'zh',
                    notifications: editedUser.preferences?.notifications || {
                      dataReminder: true,
                      goalAchievement: true,
                      suggestions: true,
                    },
                  },
                })
              }
            >
              <Text
                style={[
                  styles.unitOptionText,
                  (editedUser.preferences?.units || 'metric') === 'metric' && styles.unitOptionTextSelected,
                ]}
              >
                {t('profile.metric')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.unitOption,
                editedUser.preferences?.units === 'imperial' && styles.unitOptionSelected,
              ]}
              onPress={() =>
                setEditedUser({
                  ...editedUser,
                  preferences: {
                    ...editedUser.preferences,
                    units: 'imperial',
                    language: editedUser.preferences?.language || 'zh',
                    notifications: editedUser.preferences?.notifications || {
                      dataReminder: true,
                      goalAchievement: true,
                      suggestions: true,
                    },
                  },
                })
              }
            >
              <Text
                style={[
                  styles.unitOptionText,
                  editedUser.preferences?.units === 'imperial' && styles.unitOptionTextSelected,
                ]}
              >
                {t('profile.imperial')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 语言设置 */}
        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>{t('profile.language')}</Text>
          <TouchableOpacity
            style={styles.languageDisplay}
            onPress={() => setLanguageSelectorVisible(true)}
          >
            <Text style={styles.languageText}>{(currentLanguage || '').toUpperCase()}</Text>
            <MaterialIcons name="chevron-right" size={20} color="#64748b" />
          </TouchableOpacity>
        </View>

        {/* 通知设置 */}
        <Text style={styles.subsectionTitle}>{t('profile.notifications')}</Text>
        
        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>{t('profile.dataReminder')}</Text>
          <Switch
            value={editedUser.preferences?.notifications?.dataReminder ?? true}
            onValueChange={(value) =>
              setEditedUser({
                ...editedUser,
                preferences: {
                  ...editedUser.preferences,
                  notifications: {
                    dataReminder: value,
                    goalAchievement: editedUser.preferences?.notifications?.goalAchievement ?? true,
                    suggestions: editedUser.preferences?.notifications?.suggestions ?? true,
                  },
                },
              })
            }
            trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
            thumbColor={Platform.OS === 'android' ? '#fff' : undefined}
          />
        </View>
        
        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>{t('profile.goalAchievement')}</Text>
          <Switch
            value={editedUser.preferences?.notifications?.goalAchievement ?? true}
            onValueChange={(value) =>
              setEditedUser({
                ...editedUser,
                preferences: {
                  ...editedUser.preferences,
                  notifications: {
                    dataReminder: editedUser.preferences?.notifications?.dataReminder ?? true,
                    goalAchievement: value,
                    suggestions: editedUser.preferences?.notifications?.suggestions ?? true,
                  },
                },
              })
            }
            trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
            thumbColor={Platform.OS === 'android' ? '#fff' : undefined}
          />
        </View>
        
        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>{t('profile.suggestions')}</Text>
          <Switch
            value={editedUser.preferences?.notifications?.suggestions ?? true}
            onValueChange={(value) =>
              setEditedUser({
                ...editedUser,
                preferences: {
                  ...editedUser.preferences,
                  notifications: {
                    dataReminder: editedUser.preferences?.notifications?.dataReminder ?? true,
                    goalAchievement: editedUser.preferences?.notifications?.goalAchievement ?? true,
                    suggestions: value,
                  },
                },
              })
            }
            trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
            thumbColor={Platform.OS === 'android' ? '#fff' : undefined}
          />
        </View>
      </View>

      {/* 操作区域 */}
      <View style={styles.section}>
        <TouchableOpacity style={styles.actionButton} onPress={handleLogout}>
          <MaterialIcons name="logout" size={20} color="#ef4444" />
          <Text style={styles.actionButtonText}>{t('common.logout')}</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.bottomSpacer} />
      <LanguageSelector
        visible={languageSelectorVisible}
        onClose={() => setLanguageSelectorVisible(false)}
        currentLanguage={currentLanguage}
        onLanguageChange={(lang) => {
          // update i18n and local display
          i18nSetLanguage(lang as 'en' | 'zh');
          setCurrentLanguage(lang);
          // update Redux global language and user preference
          dispatch(setLanguage(lang as 'zh' | 'en'));
        }}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
    marginTop: 50,
  },
  
  // 头部区域
  headerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 15,
  },
  avatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#e5e7eb',
    justifyContent: 'center',
    alignItems: 'center',
  },
  changeAvatarButton: {
    position: 'absolute',
    right: -5,
    bottom: -5,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  nameInput: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    borderBottomWidth: 1,
    borderBottomColor: '#3b82f6',
    paddingVertical: 2,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 8,
  },
  providerBadge: {
    alignSelf: 'flex-start',
    backgroundColor: '#dbeafe',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  providerText: {
    fontSize: 12,
    color: '#3b82f6',
    fontWeight: '500',
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#dbeafe',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  cancelButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#fee2e2',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  
  // 区块样式
  section: {
    backgroundColor: '#fff',
    marginBottom: 15,
    paddingHorizontal: 20,
    paddingVertical: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 15,
  },
  subsectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginTop: 15,
    marginBottom: 10,
  },
  
  // 信息行
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  infoLabel: {
    fontSize: 16,
    color: '#374151',
    flex: 1,
  },
  infoValue: {
    fontSize: 16,
    color: '#1f2937',
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  infoInput: {
    fontSize: 16,
    color: '#1f2937',
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
    borderBottomWidth: 1,
    borderBottomColor: '#3b82f6',
    paddingVertical: 2,
  },
  
  // 性别选择器
  genderSelector: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  genderOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginLeft: 8,
    borderRadius: 15,
    backgroundColor: '#f3f4f6',
  },
  genderOptionSelected: {
    backgroundColor: '#3b82f6',
  },
  genderOptionText: {
    fontSize: 14,
    color: '#6b7280',
  },
  genderOptionTextSelected: {
    color: '#fff',
  },
  
  // 活动水平选择器
  activitySelector: {
    flex: 1,
    alignItems: 'flex-end',
  },
  activityOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginBottom: 6,
    borderRadius: 15,
    backgroundColor: '#f3f4f6',
  },
  activityOptionSelected: {
    backgroundColor: '#3b82f6',
  },
  activityOptionText: {
    fontSize: 14,
    color: '#6b7280',
  },
  activityOptionTextSelected: {
    color: '#fff',
  },
  
  // 设置行
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  settingLabel: {
    fontSize: 16,
    color: '#374151',
    flex: 1,
  },
  
  // 语言显示
  languageDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageText: {
    fontSize: 16,
    color: '#6b7280',
    marginRight: 8,
  },
  
  // 单位选择器
  unitSelector: {
    flexDirection: 'row',
  },
  unitOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginLeft: 8,
    borderRadius: 18,
    backgroundColor: '#f3f4f6',
  },
  unitOptionSelected: {
    backgroundColor: '#3b82f6',
  },
  unitOptionText: {
    fontSize: 14,
    color: '#6b7280',
  },
  unitOptionTextSelected: {
    color: '#fff',
  },
  
  // 操作按钮
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    borderRadius: 10,
    backgroundColor: '#fee2e2',
  },
  actionButtonText: {
    fontSize: 16,
    color: '#ef4444',
    fontWeight: '500',
    marginLeft: 8,
  },
  
  bottomSpacer: {
    height: 50,
  },
});

export default ProfileScreen;