import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import NotificationService from '../services/NotificationService';

interface NotificationSettings {
  dailyReminder: boolean;
  weighingReminder: boolean;
  aiSuggestions: boolean;
  dataUpdates: boolean;
  reminderTime: { hour: number; minute: number };
  weighingDays: number[];
}

const NotificationSettingsScreen: React.FC = () => {
  const [settings, setSettings] = useState<NotificationSettings>({
    dailyReminder: true,
    weighingReminder: true,
    aiSuggestions: true,
    dataUpdates: false,
    reminderTime: { hour: 9, minute: 0 },
    weighingDays: [1, 3, 5], // 周一、三、五
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [permissionGranted, setPermissionGranted] = useState(false);

  useEffect(() => {
    initializeNotifications();
  }, []);

  const initializeNotifications = async () => {
    try {
      const token = await NotificationService.registerForPushNotifications();
      setPermissionGranted(!!token);
      
      // 设置通知监听器
      NotificationService.setupNotificationListeners();
    } catch (error) {
      console.error('初始化通知失败:', error);
      Alert.alert('提示', '推送通知权限被拒绝，部分功能可能不可用');
    }
  };

  const updateSetting = async (key: keyof NotificationSettings, value: any) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);

    // 根据设置更新通知
    await applyNotificationSettings(newSettings);
  };

  const applyNotificationSettings = async (newSettings: NotificationSettings) => {
    setIsLoading(true);
    
    try {
      // 取消所有现有通知
      await NotificationService.cancelAllNotifications();

      // 根据设置重新安排通知
      if (newSettings.dailyReminder && permissionGranted) {
        await NotificationService.scheduleDailyReminder(
          newSettings.reminderTime.hour,
          newSettings.reminderTime.minute
        );
      }

      if (newSettings.weighingReminder && permissionGranted) {
        await NotificationService.scheduleWeighingReminder(
          newSettings.weighingDays,
          newSettings.reminderTime.hour,
          newSettings.reminderTime.minute
        );
      }

      console.log('通知设置已更新');
    } catch (error) {
      console.error('更新通知设置失败:', error);
      Alert.alert('错误', '更新通知设置失败');
    } finally {
      setIsLoading(false);
    }
  };

  const showTimePickerAlert = () => {
    Alert.prompt(
      '设置提醒时间',
      '请输入小时（0-23）',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确定',
          onPress: (hourText?: string) => {
            if (hourText) {
              const hour = parseInt(hourText);
              if (hour >= 0 && hour <= 23) {
                Alert.prompt(
                  '设置提醒时间',
                  '请输入分钟（0-59）',
                  [
                    { text: '取消', style: 'cancel' },
                    {
                      text: '确定',
                      onPress: (minuteText?: string) => {
                        if (minuteText) {
                          const minute = parseInt(minuteText);
                          if (minute >= 0 && minute <= 59) {
                            updateSetting('reminderTime', { hour, minute });
                          } else {
                            Alert.alert('错误', '分钟必须在0-59之间');
                          }
                        }
                      },
                    },
                  ],
                  'plain-text',
                  settings.reminderTime.minute.toString()
                );
              } else {
                Alert.alert('错误', '小时必须在0-23之间');
              }
            }
          },
        },
      ],
      'plain-text',
      settings.reminderTime.hour.toString()
    );
  };

  const toggleWeighingDay = (day: number) => {
    const newDays = settings.weighingDays.includes(day)
      ? settings.weighingDays.filter(d => d !== day)
      : [...settings.weighingDays, day].sort();
    
    updateSetting('weighingDays', newDays);
  };

  const getDayName = (day: number) => {
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    return days[day];
  };

  const testNotification = async () => {
    if (!permissionGranted) {
      Alert.alert('提示', '请先开启推送通知权限');
      return;
    }

    try {
      await NotificationService.scheduleImmediateNotification(
        '测试通知',
        '这是一条测试通知，说明推送功能正常工作！',
        { type: 'test' }
      );
      Alert.alert('成功', '测试通知已发送');
    } catch (error) {
      Alert.alert('错误', '发送测试通知失败');
    }
  };

  const SettingItem: React.FC<{
    title: string;
    description?: string;
    value: boolean;
    onValueChange: (value: boolean) => void;
    icon?: string;
  }> = ({ title, description, value, onValueChange, icon }) => (
    <View style={styles.settingItem}>
      <View style={styles.settingInfo}>
        {icon && (
          <Ionicons 
            name={icon as any} 
            size={20} 
            color="#6366f1" 
            style={styles.settingIcon} 
          />
        )}
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          {description && (
            <Text style={styles.settingDescription}>{description}</Text>
          )}
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: '#f3f4f6', true: '#ddd6fe' }}
        thumbColor={value ? '#6366f1' : '#9ca3af'}
      />
    </View>
  );

  if (!permissionGranted) {
    return (
      <View style={styles.container}>
        <View style={styles.permissionContainer}>
          <Ionicons name="notifications-off" size={64} color="#9ca3af" />
          <Text style={styles.permissionTitle}>需要通知权限</Text>
          <Text style={styles.permissionDescription}>
            为了给您发送健康提醒和数据更新，应用需要推送通知权限。
          </Text>
          <TouchableOpacity 
            style={styles.permissionButton}
            onPress={initializeNotifications}
          >
            <Text style={styles.permissionButtonText}>授权通知</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>通知设置</Text>
        <TouchableOpacity onPress={testNotification} style={styles.testButton}>
          <Ionicons name="notifications" size={16} color="#6366f1" />
          <Text style={styles.testButtonText}>测试</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>提醒设置</Text>
        
        <SettingItem
          title="每日提醒"
          description="每天提醒您记录体脂数据"
          value={settings.dailyReminder}
          onValueChange={(value) => updateSetting('dailyReminder', value)}
          icon="alarm"
        />

        <SettingItem
          title="测量提醒"
          description="在指定日期提醒您进行体脂测量"
          value={settings.weighingReminder}
          onValueChange={(value) => updateSetting('weighingReminder', value)}
          icon="fitness"
        />

        <SettingItem
          title="AI建议通知"
          description="接收个性化健康建议推送"
          value={settings.aiSuggestions}
          onValueChange={(value) => updateSetting('aiSuggestions', value)}
          icon="bulb"
        />

        <SettingItem
          title="数据更新"
          description="数据同步完成后通知"
          value={settings.dataUpdates}
          onValueChange={(value) => updateSetting('dataUpdates', value)}
          icon="sync"
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>时间设置</Text>
        
        <TouchableOpacity style={styles.timeSelector} onPress={showTimePickerAlert}>
          <View style={styles.timeSelectorInfo}>
            <Ionicons name="time" size={20} color="#6366f1" />
            <Text style={styles.timeSelectorTitle}>提醒时间</Text>
          </View>
          <View style={styles.timeSelectorValue}>
            <Text style={styles.timeText}>
              {String(settings.reminderTime.hour).padStart(2, '0')}:
              {String(settings.reminderTime.minute).padStart(2, '0')}
            </Text>
            <Ionicons name="chevron-forward" size={16} color="#9ca3af" />
          </View>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>测量日期</Text>
        <Text style={styles.sectionDescription}>选择每周的测量提醒日期</Text>
        
        <View style={styles.daySelector}>
          {[1, 2, 3, 4, 5, 6, 0].map((day) => (
            <TouchableOpacity
              key={day}
              style={[
                styles.dayButton,
                settings.weighingDays.includes(day) && styles.selectedDayButton,
              ]}
              onPress={() => toggleWeighingDay(day)}
            >
              <Text
                style={[
                  styles.dayButtonText,
                  settings.weighingDays.includes(day) && styles.selectedDayButtonText,
                ]}
              >
                {getDayName(day)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          * 推送通知需要网络连接，某些设置可能因系统限制而有所不同
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f9ff',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  testButtonText: {
    marginLeft: 4,
    color: '#6366f1',
    fontSize: 14,
    fontWeight: '500',
  },
  section: {
    backgroundColor: '#fff',
    marginTop: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    paddingHorizontal: 20,
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6b7280',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  settingDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  timeSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  timeSelectorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeSelectorTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginLeft: 12,
  },
  timeSelectorValue: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6366f1',
    marginRight: 8,
  },
  daySelector: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
  },
  dayButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedDayButton: {
    backgroundColor: '#6366f1',
  },
  dayButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
  },
  selectedDayButtonText: {
    color: '#fff',
  },
  footer: {
    padding: 20,
    marginTop: 20,
  },
  footerText: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
    lineHeight: 16,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginTop: 16,
    marginBottom: 8,
  },
  permissionDescription: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  permissionButton: {
    backgroundColor: '#6366f1',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default NotificationSettingsScreen;