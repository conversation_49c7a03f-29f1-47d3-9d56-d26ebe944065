import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { RootState, AppDispatch } from '../store';
import {
  generateSuggestions,
  fetchSuggestions,
  removeSuggestion,
} from '../store/slices/suggestionsSlice';
import { fetchBodyData } from '../store/slices/bodyDataSlice';
import { HealthSuggestion } from '../types';

const SuggestionsScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { data: bodyData } = useSelector((state: RootState) => state.bodyData);
  const { suggestions, isLoading, error } = useSelector(
    (state: RootState) => state.suggestions
  );
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (user) {
      dispatch(fetchSuggestions(user._id));
      dispatch(fetchBodyData(user._id));
    }
  }, [dispatch, user]);

  const handleGenerateSuggestions = async () => {
    if (!user) {
      Alert.alert('错误', '请先登录');
      return;
    }

    if (bodyData.length === 0) {
      Alert.alert(
        '提示',
        '请先记录体脂数据，以便获取更精准的建议'
      );
    }

    try {
      await dispatch(generateSuggestions({ userId: user._id, bodyData })).unwrap();
      Alert.alert('成功', '已生成新的智能建议');
    } catch (error) {
      Alert.alert('错误', '生成建议失败，请稍后重试');
    }
  };

  const onRefresh = async () => {
    if (!user) return;
    setRefreshing(true);
    try {
      await dispatch(fetchSuggestions(user._id)).unwrap();
    } catch (error) {
      console.error('刷新失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleDeleteSuggestion = (suggestion: HealthSuggestion) => {
    Alert.alert(
      '删除建议',
      '确定要删除这条建议吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => dispatch(removeSuggestion(suggestion.id)),
        },
      ]
    );
  };

  const getCategoryIcon = (category: HealthSuggestion['category']) => {
    switch (category) {
      case 'diet':
        return 'restaurant';
      case 'exercise':
        return 'fitness';
      case 'lifestyle':
        return 'bed';
      default:
        return 'bulb';
    }
  };

  const getCategoryColor = (category: HealthSuggestion['category']) => {
    switch (category) {
      case 'diet':
        return '#f59e0b';
      case 'exercise':
        return '#10b981';
      case 'lifestyle':
        return '#8b5cf6';
      default:
        return '#6366f1';
    }
  };

  const getCategoryName = (category: HealthSuggestion['category']) => {
    switch (category) {
      case 'diet':
        return '饮食';
      case 'exercise':
        return '运动';
      case 'lifestyle':
        return '生活方式';
      default:
        return '通用建议';
    }
  };

  const renderSuggestionItem = (suggestion: HealthSuggestion) => (
    <View key={suggestion.id} style={styles.suggestionCard}>
      <View style={styles.suggestionHeader}>
        <View style={styles.categoryContainer}>
          <Ionicons
            name={getCategoryIcon(suggestion.category)}
            size={20}
            color={getCategoryColor(suggestion.category)}
          />
          <Text
            style={[
              styles.categoryText,
              { color: getCategoryColor(suggestion.category) },
            ]}
          >
            {getCategoryName(suggestion.category)}
          </Text>
        </View>
        <TouchableOpacity
          onPress={() => handleDeleteSuggestion(suggestion)}
          style={styles.deleteButton}
        >
          <Ionicons name="close" size={18} color="#9ca3af" />
        </TouchableOpacity>
      </View>
      
      <Text style={styles.suggestionContent}>{suggestion.content}</Text>
      
      <View style={styles.suggestionFooter}>
        <Text style={styles.suggestionDate}>
          {new Date(suggestion.createdAt).toLocaleDateString('zh-CN')}
        </Text>
        <View style={styles.aiTag}>
          <Ionicons name="sparkles" size={12} color="#6366f1" />
          <Text style={styles.aiText}>AI生成</Text>
        </View>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="bulb-outline" size={64} color="#9ca3af" />
      <Text style={styles.emptyTitle}>暂无智能建议</Text>
      <Text style={styles.emptySubtitle}>
        点击下方按钮，基于您的体脂数据生成个性化健康建议
      </Text>
      <TouchableOpacity
        style={styles.generateButton}
        onPress={handleGenerateSuggestions}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#fff" size="small" />
        ) : (
          <>
            <Ionicons name="sparkles" size={16} color="#fff" />
            <Text style={styles.generateButtonText}>生成AI建议</Text>
          </>
        )}
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>智能建议</Text>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={handleGenerateSuggestions}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#6366f1" />
          ) : (
            <Ionicons name="refresh" size={20} color="#6366f1" />
          )}
        </TouchableOpacity>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Ionicons name="warning" size={20} color="#ef4444" />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {suggestions.length > 0 ? (
          <>
            <View style={styles.statsContainer}>
              <Text style={styles.statsText}>
                共有 {suggestions.length} 条建议
              </Text>
            </View>
            {suggestions.map(renderSuggestionItem)}
          </>
        ) : (
          renderEmptyState()
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  headerButton: {
    padding: 8,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef2f2',
    padding: 12,
    margin: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  errorText: {
    marginLeft: 8,
    color: '#dc2626',
    flex: 1,
  },
  content: {
    flex: 1,
  },
  statsContainer: {
    padding: 20,
    paddingBottom: 10,
  },
  statsText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  suggestionCard: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  suggestionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  deleteButton: {
    padding: 4,
  },
  suggestionContent: {
    fontSize: 16,
    lineHeight: 24,
    color: '#374151',
    marginBottom: 12,
  },
  suggestionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  suggestionDate: {
    fontSize: 12,
    color: '#9ca3af',
  },
  aiTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f9ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  aiText: {
    fontSize: 11,
    color: '#6366f1',
    fontWeight: '500',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  generateButton: {
    backgroundColor: '#6366f1',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 24,
  },
  generateButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
});

export default SuggestionsScreen;