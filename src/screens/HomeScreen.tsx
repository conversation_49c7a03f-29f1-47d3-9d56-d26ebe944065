import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { RootState, AppDispatch } from '../store';
import { HealthSuggestion } from '../types';
import { fetchBodyData } from '../store/slices/bodyDataSlice';
import { generateSuggestions } from '../store/slices/suggestionsSlice';
import { useBluetoothConnection } from '../hooks/useBluetoothHooks';
import BodyDataInputModal from '../components/BodyDataInputModal';
import { t } from '../i18n';

const HomeScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { data: bodyData } = useSelector((state: RootState) => state.bodyData);
  const { suggestions } = useSelector((state: RootState) => state.suggestions);
  const { isConnected, startDataMonitoring } = useBluetoothConnection();
  const [showInputModal, setShowInputModal] = useState(false);

  useEffect(() => {
    if (user) {
      dispatch(fetchBodyData(user._id));
    }
  }, [dispatch, user]);

  // 自动开始数据监听
  useEffect(() => {
    if (isConnected) {
      startDataMonitoring();
    }
  }, [isConnected, startDataMonitoring]);

  const latestData = bodyData[0];

  const handleGenerateAdvice = () => {
    if (user && bodyData.length > 0) {
      dispatch(generateSuggestions({ userId: user._id, bodyData }));
    }
  };

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'connect':
        // 导航到设备页面
        break;
      case 'input':
        setShowInputModal(true);
        break;
      case 'trends':
        // 导航到数据页面
        break;
      case 'settings':
        // 导航到设置页面
        break;
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.greeting}>{t('home.greeting', { name: user?.nickname })}</Text>
        <Text style={styles.subtitle}>{t('home.subtitle')}</Text>
      </View>

      {latestData && (
        <View style={styles.dataCard}>
          <Text style={styles.cardTitle}>{t('home.latestData')}</Text>
          <View style={styles.dataGrid}>
            <View style={styles.dataItem}>
              <Ionicons name="body" size={24} color="#6366f1" />
              <Text style={styles.dataValue}>{latestData.weight.toFixed(1)}{t('home.kg')}</Text>
              <Text style={styles.dataLabel}>{t('home.weight')}</Text>
            </View>
            <View style={styles.dataItem}>
              <Ionicons name="analytics" size={24} color="#ef4444" />
              <Text style={styles.dataValue}>{latestData.bodyFatPercentage.toFixed(1)}%</Text>
              <Text style={styles.dataLabel}>{t('home.bodyFat')}</Text>
            </View>
            <View style={styles.dataItem}>
              <Ionicons name="fitness" size={24} color="#10b981" />
              <Text style={styles.dataValue}>{latestData.muscleMass.toFixed(1)}{t('home.kg')}</Text>
              <Text style={styles.dataLabel}>{t('home.muscleMass')}</Text>
            </View>
            <View style={styles.dataItem}>
              <Ionicons name="speedometer" size={24} color="#f59e0b" />
              <Text style={styles.dataValue}>{latestData.bmi.toFixed(1)}</Text>
              <Text style={styles.dataLabel}>{t('home.bmi')}</Text>
            </View>
          </View>
        </View>
      )}

      <View style={styles.actionCard}>
        <Text style={styles.cardTitle}>{t('home.smartSuggestions')}</Text>
        {suggestions.length > 0 ? (
          <View>
            {suggestions.slice(0, 2).map((suggestion: HealthSuggestion) => (
              <View key={suggestion.id} style={styles.suggestionItem}>
                <Ionicons name="bulb" size={20} color="#6366f1" />
                <Text style={styles.suggestionText}>{suggestion.content}</Text>
              </View>
            ))}
          </View>
        ) : (
          <Text style={styles.noSuggestions}>{t('home.noSuggestions')}</Text>
        )}
        
        <TouchableOpacity style={styles.generateButton} onPress={handleGenerateAdvice}>
          <Text style={styles.generateButtonText}>{t('home.getAIAdvice')}</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.quickActions}>
        <Text style={styles.cardTitle}>{t('home.quickActions')}</Text>
        <View style={styles.actionGrid}>
          <TouchableOpacity 
            style={styles.actionItem}
            onPress={() => handleQuickAction('connect')}
          >
            <View style={[styles.actionIcon, isConnected && styles.connectedIcon]}>
              <Ionicons name="bluetooth" size={32} color={isConnected ? '#10b981' : '#6366f1'} />
              {isConnected && (
                <View style={styles.connectedBadge}>
                  <View style={styles.connectedDot} />
                </View>
              )}
            </View>
            <Text style={styles.actionText}>{t('home.connectDevice')}</Text>
            {isConnected && <Text style={styles.statusText}>{t('home.connected')}</Text>}
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.actionItem}
            onPress={() => handleQuickAction('input')}
          >
            <Ionicons name="add-circle" size={32} color="#10b981" />
            <Text style={styles.actionText}>{t('home.manualInput')}</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.actionItem}
            onPress={() => handleQuickAction('trends')}
          >
            <Ionicons name="bar-chart" size={32} color="#f59e0b" />
            <Text style={styles.actionText}>{t('home.viewTrends')}</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.actionItem}
            onPress={() => handleQuickAction('settings')}
          >
            <Ionicons name="settings" size={32} color="#64748b" />
            <Text style={styles.actionText}>{t('home.settings')}</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <BodyDataInputModal
        visible={showInputModal}
        onClose={() => setShowInputModal(false)}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 20,
    paddingTop: 40,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    marginTop: 4,
  },
  dataCard: {
    backgroundColor: '#fff',
    margin: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  dataGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  dataItem: {
    width: '48%',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    marginBottom: 12,
  },
  dataValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 8,
  },
  dataLabel: {
    fontSize: 14,
    color: '#64748b',
    marginTop: 4,
  },
  actionCard: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#f0f9ff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  suggestionText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
  },
  noSuggestions: {
    textAlign: 'center',
    color: '#64748b',
    fontStyle: 'italic',
    marginBottom: 16,
  },
  generateButton: {
    backgroundColor: '#6366f1',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  generateButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  quickActions: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionItem: {
    width: '48%',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    marginBottom: 12,
  },
  actionIcon: {
    position: 'relative',
  },
  connectedIcon: {
    opacity: 1,
  },
  connectedBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: '#10b981',
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  connectedDot: {
    width: 6,
    height: 6,
    backgroundColor: '#fff',
    borderRadius: 3,
  },
  actionText: {
    fontSize: 14,
    color: '#374151',
    marginTop: 8,
    textAlign: 'center',
  },
  statusText: {
    fontSize: 12,
    color: '#10b981',
    marginTop: 2,
    fontWeight: '500',
  },
});

export default HomeScreen;