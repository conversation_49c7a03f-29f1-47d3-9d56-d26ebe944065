import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  ScrollView,
  Alert,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { RootState, AppDispatch } from '../store';
import { fetchBodyData } from '../store/slices/bodyDataSlice';
import BodyDataList from '../components/BodyDataList';
import BodyDataInputModal from '../components/BodyDataInputModal';
import BodyDataChart from '../components/BodyDataChart';
import { BodyCompositionData } from '../types';

const DataScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { data, isLoading, error } = useSelector((state: RootState) => state.bodyData);
  const [refreshing, setRefreshing] = useState(false);
  const [showInputModal, setShowInputModal] = useState(false);

  useEffect(() => {
    if (user) {
      dispatch(fetchBodyData(user.id));
    }
  }, [dispatch, user]);

  const onRefresh = async () => {
    if (!user) return;
    setRefreshing(true);
    try {
      await dispatch(fetchBodyData(user.id)).unwrap();
    } catch (error) {
      Alert.alert('错误', '刷新数据失败');
    } finally {
      setRefreshing(false);
    }
  };

  const handleDeleteItem = async (item: BodyCompositionData) => {
    try {
      // 这里应该调用删除API
      Alert.alert('成功', '数据已删除');
      // 重新获取数据
      if (user) {
        dispatch(fetchBodyData(user.id));
      }
    } catch (error) {
      Alert.alert('错误', '删除失败');
    }
  };

  const getLatestData = () => {
    if (data.length === 0) return null;
    return data[0];
  };

  const getDataTrend = () => {
    if (data.length < 2) return null;
    const latest = data[0];
    const previous = data[1];
    
    return {
      weightChange: latest.weight - previous.weight,
      bodyFatChange: latest.bodyFatPercentage - previous.bodyFatPercentage,
      muscleChange: latest.muscleMass - previous.muscleMass,
    };
  };

  const latestData = getLatestData();
  const trend = getDataTrend();

  const TrendIndicator: React.FC<{ value: number; unit: string }> = ({ value, unit }) => {
    const isPositive = value > 0;
    const isZero = Math.abs(value) < 0.1;
    
    if (isZero) {
      return (
        <View style={styles.trendNeutral}>
          <Ionicons name="remove" size={12} color="#6b7280" />
          <Text style={styles.trendTextNeutral}>0{unit}</Text>
        </View>
      );
    }

    return (
      <View style={isPositive ? styles.trendPositive : styles.trendNegative}>
        <Ionicons 
          name={isPositive ? "arrow-up" : "arrow-down"} 
          size={12} 
          color={isPositive ? "#16a34a" : "#dc2626"} 
        />
        <Text style={isPositive ? styles.trendTextPositive : styles.trendTextNegative}>
          {Math.abs(value).toFixed(1)}{unit}
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>数据分析</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowInputModal(true)}
        >
          <Ionicons name="add" size={20} color="#fff" />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {latestData && (
          <View style={styles.summaryCard}>
            <Text style={styles.cardTitle}>最新数据</Text>
            <View style={styles.summaryGrid}>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryValue}>{latestData.weight.toFixed(1)}</Text>
                <Text style={styles.summaryLabel}>体重 (kg)</Text>
                {trend && <TrendIndicator value={trend.weightChange} unit="kg" />}
              </View>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryValue}>{latestData.bodyFatPercentage.toFixed(1)}</Text>
                <Text style={styles.summaryLabel}>体脂率 (%)</Text>
                {trend && <TrendIndicator value={trend.bodyFatChange} unit="%" />}
              </View>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryValue}>{latestData.muscleMass.toFixed(1)}</Text>
                <Text style={styles.summaryLabel}>肌肉量 (kg)</Text>
                {trend && <TrendIndicator value={trend.muscleChange} unit="kg" />}
              </View>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryValue}>{latestData.bmi.toFixed(1)}</Text>
                <Text style={styles.summaryLabel}>BMI</Text>
              </View>
            </View>
          </View>
        )}

        <BodyDataChart data={data} />

        <View style={styles.historyCard}>
          <View style={styles.historyHeader}>
            <Text style={styles.cardTitle}>历史数据</Text>
            <Text style={styles.historyCount}>共 {data.length} 条记录</Text>
          </View>
          
          <BodyDataList
            data={data}
            onDeleteItem={handleDeleteItem}
          />
        </View>
      </ScrollView>

      <BodyDataInputModal
        visible={showInputModal}
        onClose={() => setShowInputModal(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  addButton: {
    backgroundColor: '#6366f1',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  summaryCard: {
    backgroundColor: '#fff',
    margin: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  summaryItem: {
    width: '48%',
    alignItems: 'center',
    paddingVertical: 16,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    marginBottom: 12,
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  trendPositive: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#dcfce7',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginTop: 4,
  },
  trendNegative: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef2f2',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginTop: 4,
  },
  trendNeutral: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginTop: 4,
  },
  trendTextPositive: {
    fontSize: 10,
    color: '#16a34a',
    fontWeight: '500',
    marginLeft: 2,
  },
  trendTextNegative: {
    fontSize: 10,
    color: '#dc2626',
    fontWeight: '500',
    marginLeft: 2,
  },
  trendTextNeutral: {
    fontSize: 10,
    color: '#6b7280',
    fontWeight: '500',
    marginLeft: 2,
  },
  historyCard: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 10,
  },
  historyCount: {
    fontSize: 14,
    color: '#6b7280',
  },
});

export default DataScreen;