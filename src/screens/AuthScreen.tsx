import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { loginWithEmail, loginWithGoogle, loginWithApple, registerWithEmail, sendEmailCode, loginWithEmailCode } from '../store/slices/authSlice';
import { AppDispatch, RootState } from '../store';

const AuthScreen: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [useCode, setUseCode] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [code, setCode] = useState('');
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, error } = useSelector((state: RootState) => state.auth);
  const [countdown, setCountdown] = useState<number>(0);

  React.useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [countdown]);

  const handleEmailAuth = () => {
    if (!email || !password) {
      Alert.alert('错误', '请填写完整信息');
      return;
    }

    if (useCode) {
      if (!code) {
        Alert.alert('错误', '请输入验证码');
        return;
      }
      dispatch(loginWithEmailCode({ email, code }));
      return;
    }

    if (isLogin) {
      dispatch(loginWithEmail({ email, password }));
    } else {
      if (!name) {
        Alert.alert('错误', '请填写姓名');
        return;
      }
      dispatch(registerWithEmail({ email, password, name }));
    }
  };

  const handleSendCode = () => {
    if (!email) {
      Alert.alert('错误', '请输入邮箱以接收验证码');
      return;
    }
    // 等待发送结果，只有在成功时启动倒计时
    (dispatch(sendEmailCode(email)) as any).unwrap()
      .then(() => {
        setCountdown(60);
        Alert.alert('已发送', '验证码已发送到邮箱（如未收到请稍后重试）');
      })
      .catch((err: any) => {
        // 确保倒计时被重置
        setCountdown(0);
        Alert.alert('发送失败', err?.message || '发送验证码失败，请稍后重试');
      });
  };

  const handleGoogleLogin = () => {
    dispatch(loginWithGoogle());
  };

  const handleAppleLogin = () => {
    dispatch(loginWithApple());
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>HealthyLife</Text>
        <Text style={styles.subtitle}>智能健康管理</Text>
      </View>

      <View style={styles.form}>
        {!isLogin && (
          <TextInput
            style={styles.input}
            placeholder="姓名"
            value={name}
            onChangeText={setName}
          />
        )}
        
        <TextInput
          style={styles.input}
          placeholder="邮箱"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />

        {useCode ? (
          <>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <TextInput
                style={[styles.input, { flex: 1 }]}
                placeholder="验证码"
                value={code}
                onChangeText={setCode}
                keyboardType="numeric"
              />
              <TouchableOpacity style={{ marginLeft: 8 }} onPress={handleSendCode} disabled={countdown > 0 || isLoading}>
                <Text style={{ color: countdown > 0 || isLoading ? '#9ca3af' : '#6366f1' }}>
                  {countdown > 0 ? `${countdown}s` : (isLoading ? '发送中...' : '发送验证码')}
                </Text>
              </TouchableOpacity>
            </View>
          </>
        ) : (
          <TextInput
            style={styles.input}
            placeholder="密码"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
        )}

        <TouchableOpacity style={styles.primaryButton} onPress={handleEmailAuth} disabled={isLoading}>
          <Text style={styles.primaryButtonText}>
            {isLogin ? '登录' : '注册'}
          </Text>
        </TouchableOpacity>

        {error && <Text style={{ color: '#ef4444', marginTop: 8 }}>{error}</Text>}

        <View style={styles.divider}>
          <Text style={styles.dividerText}>或</Text>
        </View>

        <TouchableOpacity style={styles.socialButton} onPress={handleGoogleLogin}>
          <Ionicons name="logo-google" size={24} color="#4285F4" />
          <Text style={styles.socialButtonText}>使用 Google 登录</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.socialButton} onPress={handleAppleLogin}>
          <Ionicons name="logo-apple" size={24} color="#000" />
          <Text style={styles.socialButtonText}>使用 Apple 登录</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.switchButton}
          onPress={() => setIsLogin(!isLogin)}
        >
          <Text style={styles.switchButtonText}>
            {isLogin ? '没有账号？注册' : '已有账号？登录'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.switchButton, { marginTop: 8 }]}
          onPress={() => setUseCode(!useCode)}
        >
          <Text style={styles.switchButtonText}>
            {useCode ? '切换到密码登录' : '使用邮箱验证码登录'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    justifyContent: 'center',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#6366f1',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
  },
  form: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    fontSize: 16,
  },
  primaryButton: {
    backgroundColor: '#6366f1',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  divider: {
    alignItems: 'center',
    marginVertical: 16,
  },
  dividerText: {
    color: '#64748b',
    fontSize: 14,
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  socialButtonText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#374151',
  },
  switchButton: {
    alignItems: 'center',
    marginTop: 16,
  },
  switchButtonText: {
    color: '#6366f1',
    fontSize: 16,
  },
});

export default AuthScreen;