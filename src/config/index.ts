/*
 * @Author: colorinstance <EMAIL>
 * @Date: 2025-09-15 20:20:27
 * @LastEditors: colorinstance <EMAIL>
 * @LastEditTime: 2025-09-16 14:56:56
 * @FilePath: \HealthyLife\src\config\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 应用配置文件
export const Config = {
  // OpenAI API配置
  openai: {
    apiKey: 'your-openai-api-key-here', // 请替换为实际的API密钥
    baseUrl: 'https://api.openai.com/v1',
    model: 'gpt-3.5-turbo',
  },

  // 后端API配置
  api: {
    baseUrl: 'http://localhost:3000/api/v1',
    timeout: 10000,
  },

  // 蓝牙配置
  bluetooth: {
    scanDuration: 10000, // 扫描时长(毫秒)
    connectionTimeout: 15000, // 连接超时(毫秒)
  },

  // 应用配置
  app: {
    name: 'HealthyLife',
    version: '1.0.0',
    defaultHeight: 1.7, // 默认身高(米)
  },

  // 第三方 OAuth 配置（可选）
  google: {
    clientId: 'YOUR_GOOGLE_CLIENT_ID',
    clientSecret: 'YOUR_GOOGLE_CLIENT_SECRET',
  },

  // 数据刷新间隔
  refreshIntervals: {
    bodyData: 30000, // 30秒
    suggestions: 300000, // 5分钟
  },
};

// 调试模式
export const DEBUG = {
  bluetooth: true,
  api: true,
  chatgpt: true,
};