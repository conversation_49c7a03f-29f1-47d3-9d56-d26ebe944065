import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import * as cors from 'cors';
import helmet from 'helmet';
import * as compression from 'compression';
import { AppModule } from './app.module';
declare const module: any;

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 安全中间件
  app.use(helmet());
  app.use(compression());

  // CORS配置
  const corsOrigins = configService.get('CORS_ORIGIN')?.split(',') || ['http://localhost:8081'];
  app.use(cors({
    origin: corsOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
  }));

  // 全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true, // 自动删除未定义的属性
    forbidNonWhitelisted: true, // 如果有未定义属性则报错
    transform: true, // 自动转换类型
    transformOptions: {
      enableImplicitConversion: true,
    },
  }));

  // API版本前缀
  app.setGlobalPrefix('api/v1');

  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('HealthyLife API')
    .setDescription('智能健康管理应用后端API文档')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addTag('Authentication', '用户认证相关接口')
    .addTag('Users', '用户管理相关接口')
    .addTag('Body Data', '体脂数据相关接口')
    .addTag('Devices', '设备管理相关接口')
    .addTag('Suggestions', 'AI建议相关接口')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  const port = configService.get('PORT', 3000);
  await app.listen(port);

  console.log(`🚀 HealthyLife API服务器启动成功!`);
  console.log(`📖 API文档地址: http://localhost:${port}/api/docs`);
  console.log(`🌐 服务器地址: http://localhost:${port}/api/v1`);

  // 支持 Webpack HMR：在热更新时正确关闭旧实例，避免端口占用
  if (module && module.hot) {
    module.hot.accept();
    module.hot.dispose(() => app.close());
  }
}

bootstrap().catch((error) => {
  console.error('应用启动失败:', error);
  process.exit(1);
});