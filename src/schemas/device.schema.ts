import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type DeviceDocument = Device & Document;

@Schema({ 
  timestamps: true,
  collection: 'devices'
})
export class Device {
  @ApiProperty({ description: '设备ID' })
  _id: Types.ObjectId;

  @ApiProperty({ description: '用户ID' })
  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  userId: Types.ObjectId;

  @ApiProperty({ description: '设备名称' })
  @Prop({ required: true, trim: true })
  name: string;

  @ApiProperty({ description: '设备类型', enum: ['body_scale', 'fitness_tracker', 'smart_watch', 'other'] })
  @Prop({ 
    required: true, 
    enum: ['body_scale', 'fitness_tracker', 'smart_watch', 'other']
  })
  type: string;

  @ApiProperty({ description: '设备品牌' })
  @Prop({ required: false })
  brand?: string;

  @ApiProperty({ description: '设备型号' })
  @Prop({ required: false })
  model?: string;

  @ApiProperty({ description: '蓝牙MAC地址' })
  @Prop({ required: true })
  bluetoothId: string;

  @ApiProperty({ description: '设备UUID' })
  @Prop({ required: false })
  deviceUuid?: string;

  @ApiProperty({ description: '设备状态', enum: ['active', 'inactive', 'error'] })
  @Prop({ 
    required: true, 
    enum: ['active', 'inactive', 'error'],
    default: 'active'
  })
  status: string;

  @ApiProperty({ description: '最后连接时间' })
  @Prop({ required: false })
  lastConnectedAt?: Date;

  @ApiProperty({ description: '最后同步时间' })
  @Prop({ required: false })
  lastSyncAt?: Date;

  @ApiProperty({ description: '固件版本' })
  @Prop({ required: false })
  firmwareVersion?: string;

  @ApiProperty({ description: '电池电量(%)' })
  @Prop({ min: 0, max: 100, required: false })
  batteryLevel?: number;

  @ApiProperty({ description: '设备配置' })
  @Prop({
    type: {
      autoSync: { type: Boolean, default: true },
      syncInterval: { type: Number, default: 300 }, // 秒
      units: {
        weight: { type: String, enum: ['kg', 'lb'], default: 'kg' },
        height: { type: String, enum: ['cm', 'in'], default: 'cm' },
      },
      notifications: { type: Boolean, default: true },
    },
    default: {}
  })
  config: {
    autoSync: boolean;
    syncInterval: number;
    units: {
      weight: string;
      height: string;
    };
    notifications: boolean;
  };

  @ApiProperty({ description: '同步统计' })
  @Prop({
    type: {
      totalSyncCount: { type: Number, default: 0 },
      successCount: { type: Number, default: 0 },
      errorCount: { type: Number, default: 0 },
      lastError: { type: String, required: false },
      lastErrorAt: { type: Date, required: false },
    },
    default: {}
  })
  syncStats: {
    totalSyncCount: number;
    successCount: number;
    errorCount: number;
    lastError?: string;
    lastErrorAt?: Date;
  };

  @ApiProperty({ description: '是否为主设备' })
  @Prop({ default: false })
  isPrimary: boolean;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export const DeviceSchema = SchemaFactory.createForClass(Device);

// 创建索引
DeviceSchema.index({ userId: 1, type: 1 });
DeviceSchema.index({ bluetoothId: 1 }, { unique: true });
DeviceSchema.index({ userId: 1, isPrimary: 1 });
DeviceSchema.index({ userId: 1, status: 1 });