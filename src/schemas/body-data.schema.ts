import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type BodyDataDocument = BodyData & Document;

@Schema({ 
  timestamps: true,
  collection: 'body_data'
})
export class BodyData {
  @ApiProperty({ description: '数据ID' })
  _id: Types.ObjectId;

  @ApiProperty({ description: '用户ID' })
  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  userId: Types.ObjectId;

  @ApiProperty({ description: '测量时间' })
  @Prop({ required: true, index: true })
  timestamp: Date;

  @ApiProperty({ description: '体重(kg)' })
  @Prop({ required: true, min: 0, max: 500 })
  weight: number;

  @ApiProperty({ description: '体脂率(%)' })
  @Prop({ required: true, min: 0, max: 100 })
  bodyFatPercentage: number;

  @ApiProperty({ description: '肌肉量(kg)' })
  @Prop({ required: true, min: 0, max: 200 })
  muscleMass: number;

  @ApiProperty({ description: '骨量(kg)' })
  @Prop({ required: true, min: 0, max: 20 })
  boneMass: number;

  @ApiProperty({ description: '水分率(%)' })
  @Prop({ required: true, min: 0, max: 100 })
  waterPercentage: number;

  @ApiProperty({ description: '内脏脂肪等级' })
  @Prop({ required: true, min: 0, max: 30 })
  visceralFat: number;

  @ApiProperty({ description: '基础代谢率(kcal/day)' })
  @Prop({ required: true, min: 0, max: 5000 })
  bmr: number;

  @ApiProperty({ description: 'BMI指数' })
  @Prop({ required: true, min: 0, max: 80 })
  bmi: number;

  @ApiProperty({ description: '数据来源设备ID' })
  @Prop({ required: true })
  deviceId: string;

  @ApiProperty({ description: '数据来源类型', enum: ['bluetooth', 'manual', 'api'] })
  @Prop({ 
    required: true, 
    enum: ['bluetooth', 'manual', 'api'],
    default: 'manual'
  })
  sourceType: string;

  @ApiProperty({ description: '数据质量评分(0-100)' })
  @Prop({ min: 0, max: 100, default: 100 })
  qualityScore: number;

  @ApiProperty({ description: '备注信息' })
  @Prop({ required: false, maxlength: 500 })
  notes?: string;

  @ApiProperty({ description: '是否已同步到云端' })
  @Prop({ default: true })
  isSynced: boolean;

  @ApiProperty({ description: '原始数据(用于调试)' })
  @Prop({ type: Object, required: false, select: false })
  rawData?: any;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export const BodyDataSchema = SchemaFactory.createForClass(BodyData);

// 创建复合索引
BodyDataSchema.index({ userId: 1, timestamp: -1 });
BodyDataSchema.index({ userId: 1, createdAt: -1 });
BodyDataSchema.index({ deviceId: 1, timestamp: -1 });

// 虚拟字段：计算健康等级
BodyDataSchema.virtual('healthGrade').get(function() {
  const bmi = this.bmi;
  const bodyFat = this.bodyFatPercentage;
  const visceralFat = this.visceralFat;
  
  let score = 100;
  
  // BMI评分
  if (bmi < 18.5 || bmi > 28) score -= 20;
  else if (bmi < 20 || bmi > 25) score -= 10;
  
  // 体脂率评分（因性别而异，这里使用通用标准）
  if (bodyFat < 10 || bodyFat > 30) score -= 20;
  else if (bodyFat < 12 || bodyFat > 25) score -= 10;
  
  // 内脏脂肪评分
  if (visceralFat > 15) score -= 20;
  else if (visceralFat > 10) score -= 10;
  
  return Math.max(0, score);
});