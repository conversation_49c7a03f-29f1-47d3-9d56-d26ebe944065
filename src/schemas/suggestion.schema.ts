import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type SuggestionDocument = Suggestion & Document;

@Schema({ 
  timestamps: true,
  collection: 'suggestions'
})
export class Suggestion {
  @ApiProperty({ description: '建议ID' })
  _id: Types.ObjectId;

  @ApiProperty({ description: '用户ID' })
  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  userId: Types.ObjectId;

  @ApiProperty({ description: '建议内容' })
  @Prop({ required: true, maxlength: 2000 })
  content: string;

  @ApiProperty({ description: '建议类别', enum: ['diet', 'exercise', 'lifestyle', 'general'] })
  @Prop({ 
    required: true, 
    enum: ['diet', 'exercise', 'lifestyle', 'general']
  })
  category: string;

  @ApiProperty({ description: '优先级', enum: ['low', 'medium', 'high'] })
  @Prop({ 
    required: true, 
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  })
  priority: string;

  @ApiProperty({ description: '基于的数据ID列表' })
  @Prop({ type: [{ type: Types.ObjectId, ref: 'BodyData' }] })
  basedOnData: Types.ObjectId[];

  @ApiProperty({ description: '生成方式', enum: ['ai', 'rule', 'manual'] })
  @Prop({ 
    required: true, 
    enum: ['ai', 'rule', 'manual'],
    default: 'ai'
  })
  generationType: string;

  @ApiProperty({ description: 'AI模型信息' })
  @Prop({
    type: {
      model: String,
      version: String,
      temperature: Number,
      tokens: Number,
    },
    required: false
  })
  aiMeta?: {
    model: string;
    version: string;
    temperature: number;
    tokens: number;
  };

  @ApiProperty({ description: '用户反馈', enum: ['helpful', 'not_helpful', 'irrelevant'] })
  @Prop({ 
    enum: ['helpful', 'not_helpful', 'irrelevant'],
    required: false
  })
  userFeedback?: string;

  @ApiProperty({ description: '反馈时间' })
  @Prop({ required: false })
  feedbackAt?: Date;

  @ApiProperty({ description: '是否已读' })
  @Prop({ default: false })
  isRead: boolean;

  @ApiProperty({ description: '是否已收藏' })
  @Prop({ default: false })
  isFavorite: boolean;

  @ApiProperty({ description: '过期时间' })
  @Prop({ required: false })
  expiresAt?: Date;

  @ApiProperty({ description: '标签' })
  @Prop({ type: [String], default: [] })
  tags: string[];

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export const SuggestionSchema = SchemaFactory.createForClass(Suggestion);

// 创建索引
SuggestionSchema.index({ userId: 1, createdAt: -1 });
SuggestionSchema.index({ userId: 1, category: 1 });
SuggestionSchema.index({ userId: 1, priority: 1 });
SuggestionSchema.index({ userId: 1, isRead: 1 });
SuggestionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL索引