import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type UserDocument = User & Document;

@Schema({ 
  timestamps: true,
  collection: 'users'
})
export class User {
  @ApiProperty({ description: '用户ID' })
  _id: Types.ObjectId;

  @ApiProperty({ description: '用户昵称' })
  @Prop({ required: true, trim: true })
  nickname: string;

  @ApiProperty({ description: '邮箱地址', required: false })
  @Prop({ required: false, lowercase: true, trim: true })
  email?: string;

  @ApiProperty({ description: '手机号码', required: false })
  @Prop({ required: false})
  phone?: string;

  @ApiProperty({ description: '密码', writeOnly: true })
  @Prop({ required: false, select: false })
  password?: string;

  @ApiProperty({ description: '头像URL', required: false })
  @Prop({ required: false })
  avatar?: string;

  @ApiProperty({ description: '是否激活', default: true })
  @Prop({ required: true, default: true })
  isActive: boolean;

  @ApiProperty({ description: '社交账户信息', required: false })
  @Prop({
    type: {
      google: {
        id: String,
        email: String,
        name: String,
      },
      apple: {
        id: String,
        email: String,
        name: String,
      },
    },
    required: false,
    default: {}
  })
  socialAccounts?: {
    google?: {
      id: string;
      email: string;
      name: string;
    };
    apple?: {
      id: string;
      email: string;
      name: string;
    };
  };

  @ApiProperty({ description: '个人资料' })
  @Prop({
    type: {
      gender: { type: String, enum: ['male', 'female', 'other'] },
      birthDate: { type: Date },
      height: { type: Number }, // 身高(cm)
      age: { type: Number }, // 年龄
      activityLevel: { type: String, enum: ['sedentary', 'light', 'moderate', 'active', 'very_active'] },
    },
    default: {}
  })
  profile: {
    gender?: 'male' | 'female' | 'other';
    birthDate?: Date;
    height?: number;
    age?: number;
    activityLevel?: 'sedentary' | 'light' | 'moderate' | 'active' | 'very_active';
  };

  @ApiProperty({ description: '用户设置' })
  @Prop({
    type: {
      notifications: {
        dailyReminder: { type: Boolean, default: true },
        weighingReminder: { type: Boolean, default: true },
        aiSuggestions: { type: Boolean, default: true },
        dataUpdates: { type: Boolean, default: false },
      },
      privacy: {
        dataSharing: { type: Boolean, default: false },
        analyticsOptOut: { type: Boolean, default: false },
      },
      preferences: {
        theme: { type: String, default: 'light' },
        language: { type: String, default: 'zh-CN' },
        heightUnit: { type: String, default: 'cm' },
        weightUnit: { type: String, default: 'kg' },
      }
    },
    default: {}
  })
  settings: {
    notifications: {
      dailyReminder: boolean;
      weighingReminder: boolean;
      aiSuggestions: boolean;
      dataUpdates: boolean;
    };
    privacy: {
      dataSharing: boolean;
      analyticsOptOut: boolean;
    };
    preferences: {
      theme: string;
      language: string;
      heightUnit: string;
      weightUnit: string;
    };
  };

  @ApiProperty({ description: '最后登录时间' })
  @Prop({ required: false })
  lastLoginAt?: Date;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export const UserSchema = SchemaFactory.createForClass(User);

// 创建索引
UserSchema.index({ email: 1 }, { unique: true, sparse: true });
UserSchema.index({ authProvider: 1, thirdPartyId: 1 });
UserSchema.index({ phone: 1 }, { unique: true, sparse: true });
UserSchema.index({ createdAt: -1 });