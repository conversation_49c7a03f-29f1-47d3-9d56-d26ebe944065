import { BodyCompositionData } from '../types';
import { Config } from '../config';

class BodyDataService {
  private static instance: BodyDataService;
  private apiBaseUrl = Config.api.baseUrl; // 使用统一的API地址配置

  public static getInstance(): BodyDataService {
    if (!BodyDataService.instance) {
      BodyDataService.instance = new BodyDataService();
    }
    return BodyDataService.instance;
  }

  // 获取用户的体脂数据
  async getBodyData(userId: string): Promise<BodyCompositionData[]> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/body-data/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // 添加认证头
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error('获取数据失败');
      }

      const data: BodyCompositionData[] = await response.json();
      return data.map(item => ({
        ...item,
        timestamp: new Date(item.timestamp),
      }));
    } catch (error) {
      console.error('获取体脂数据失败:', error);
      // 开发阶段返回模拟数据
      return this.getMockBodyData(userId);
    }
  }

  // 添加新的体脂数据
  async addBodyData(data: Omit<BodyCompositionData, 'id'>): Promise<BodyCompositionData> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/body-data`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
        body: JSON.stringify({
          ...data,
          timestamp: data.timestamp.toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('添加数据失败');
      }

      const responseData = await response.json();
      return {
        ...responseData,
        timestamp: new Date(responseData.timestamp),
      };
    } catch (error) {
      console.error('添加体脂数据失败:', error);
      // 开发阶段返回模拟数据
      return {
        id: Date.now().toString(),
        ...data,
      };
    }
  }

  // 删除体脂数据
  async deleteBodyData(dataId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/body-data/${dataId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
      });

      return response.ok;
    } catch (error) {
      console.error('删除体脂数据失败:', error);
      return false;
    }
  }

  // 获取数据统计
  async getDataStatistics(userId: string, period: 'week' | 'month' | 'year'): Promise<any> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/body-data/${userId}/statistics?period=${period}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error('获取统计数据失败');
      }

      return await response.json();
    } catch (error) {
      console.error('获取统计数据失败:', error);
      return this.getMockStatistics(period);
    }
  }

  // 计算BMI
  calculateBMI(weight: number, height: number): number {
    return weight / (height * height);
  }

  // 计算基础代谢率 (BMR)
  calculateBMR(weight: number, height: number, age: number, gender: 'male' | 'female'): number {
    if (gender === 'male') {
      return 88.362 + (13.397 * weight) + (4.799 * height * 100) - (5.677 * age);
    } else {
      return 447.593 + (9.247 * weight) + (3.098 * height * 100) - (4.330 * age);
    }
  }

  // 分析体脂数据趋势
  analyzeDataTrend(data: BodyCompositionData[]): {
    weightTrend: 'increasing' | 'decreasing' | 'stable';
    bodyFatTrend: 'increasing' | 'decreasing' | 'stable';
    muscleTrend: 'increasing' | 'decreasing' | 'stable';
  } {
    if (data.length < 2) {
      return {
        weightTrend: 'stable',
        bodyFatTrend: 'stable',
        muscleTrend: 'stable',
      };
    }

    const sortedData = data.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    const recentData = sortedData.slice(-5); // 最近5次数据

    const weightTrend = this.calculateTrend(recentData.map(d => d.weight));
    const bodyFatTrend = this.calculateTrend(recentData.map(d => d.bodyFatPercentage));
    const muscleTrend = this.calculateTrend(recentData.map(d => d.muscleMass));

    return {
      weightTrend,
      bodyFatTrend,
      muscleTrend,
    };
  }

  // 计算趋势
  private calculateTrend(values: number[]): 'increasing' | 'decreasing' | 'stable' {
    if (values.length < 2) return 'stable';

    const firstValue = values[0];
    const lastValue = values[values.length - 1];
    const change = ((lastValue - firstValue) / firstValue) * 100;

    if (change > 2) return 'increasing';
    if (change < -2) return 'decreasing';
    return 'stable';
  }

  // 获取认证token
  private async getAuthToken(): Promise<string> {
    // 这里应该从安全存储中获取token
    // 例如使用 expo-secure-store
    return 'mock_token';
  }

  // 获取模拟数据（开发用）
  private getMockBodyData(userId: string): BodyCompositionData[] {
    const mockData: BodyCompositionData[] = [];
    const now = new Date();

    for (let i = 0; i < 30; i++) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      mockData.push({
        id: `mock_${i}`,
        userId,
        timestamp: date,
        weight: 70 + Math.random() * 10 - 5,
        bodyFatPercentage: 15 + Math.random() * 10,
        muscleMass: 30 + Math.random() * 5,
        boneMass: 2.5 + Math.random() * 0.5,
        waterPercentage: 55 + Math.random() * 10,
        visceralFat: 5 + Math.random() * 3,
        bmr: 1500 + Math.random() * 300,
        bmi: 22 + Math.random() * 3,
        deviceId: 'mock_device',
      });
    }

    return mockData.reverse();
  }

  // 获取模拟统计数据
  private getMockStatistics(period: string) {
    return {
      period,
      averageWeight: 70.5,
      averageBodyFat: 18.2,
      averageMuscleMass: 32.1,
      weightChange: -1.2,
      bodyFatChange: -0.8,
      muscleMassChange: 0.5,
    };
  }
}

export default BodyDataService.getInstance();