import * as AuthSession from 'expo-auth-session';
import * as <PERSON><PERSON><PERSON><PERSON> from 'expo-web-browser';
import * as Crypto from 'expo-crypto';
import { User } from '../types';
import { request } from '../utils/request';

// Ensure WebBrowser can complete auth session
WebBrowser.maybeCompleteAuthSession();

// Optional Apple authentication
let AppleAuthentication: any = null;
try {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  AppleAuthentication = require('expo-apple-authentication');
} catch (err) {
  AppleAuthentication = null;
}

class AuthService {
  private static instance: AuthService;
  private apiBaseUrl = (require('../config').Config.api.baseUrl) as string;

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  // 邮箱登录
  async loginWithEmail(email: string, password: string): Promise<any> {
    const response = await request(`${this.apiBaseUrl}/auth/login`, {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
    if (!response.ok) {
      const text = await response.text();
      throw new Error(text || '登录失败');
    }
    const payload = await response.json();
    return payload; // { user, access_token }
  }

  // 注册
  async registerWithEmail(email: string, password: string, name: string): Promise<any> {
    const response = await request(`${this.apiBaseUrl}/auth/register`, {
      method: 'POST',
      body: JSON.stringify({ email, password, name }),
    });
    if (!response.ok) {
      const text = await response.text();
      throw new Error(text || '注册失败');
    }
    const payload = await response.json();
    return payload;
  }

  // Google login placeholder (component side handles OAuth flow)
  async loginWithGoogle(): Promise<any> {
    throw new Error('请在组件端使用 OAuth 流程进行 Google 登录');
  }

  // Apple 登录（优先使用 expo-apple-authentication，若不可用则返回模拟）
  async loginWithApple(): Promise<any> {
    if (AppleAuthentication && AppleAuthentication.signInAsync) {
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });
      if (!credential || !credential.identityToken) {
        throw new Error('Apple 登录未返回 identityToken');
      }
      const response = await request(`${this.apiBaseUrl}/auth/social-login`, {
        method: 'POST',
        body: JSON.stringify({ accessToken: credential.identityToken, provider: 'apple' }),
      });
      if (!response.ok) {
        const text = await response.text();
        throw new Error(text || 'Apple 登录失败');
      }
      const payload = await response.json();
      return payload;
    }

    // Fallback for dev
    return {
      user: {
        id: '1',
        email: '<EMAIL>',
        name: 'Apple User',
        authProvider: 'apple',
        createdAt: new Date(),
      },
      access_token: null,
    };
  }

  // 社交登录（将第三方 access token 发给后端）
  async socialLogin(accessToken: string, provider: 'google' | 'apple'): Promise<any> {
    const response = await request(`${this.apiBaseUrl}/auth/social-login`, {
      method: 'POST',
      body: JSON.stringify({ accessToken, provider }),
    });
    if (!response.ok) {
      const text = await response.text();
      throw new Error(text || '社交登录失败');
    }
    const payload = await response.json();
    return payload;
  }

  // 手机号登录
  async loginWithPhone(phone: string, verificationCode: string): Promise<any> {
    const response = await request(`${this.apiBaseUrl}/auth/phone`, {
      method: 'POST',
      body: JSON.stringify({ phone, verificationCode }),
    });
    if (!response.ok) {
      const text = await response.text();
      throw new Error(text || '手机验证失败');
    }
    const userData = await response.json();
    return userData;
  }

  // 发送验证码
  async sendVerificationCode(identifier: string, type: 'login' | 'register' | 'reset_password' = 'login'): Promise<boolean> {
    const response = await request(`${this.apiBaseUrl}/auth/send-verification-code`, {
      method: 'POST',
      body: JSON.stringify({ identifier, type }),
    });
    if (!response.ok) {
      const text = await response.text();
      throw new Error(text || '发送验证码失败');
    }
    return true;
  }

  // Compatibility helper used by authSlice
  async sendEmailCode(email: string): Promise<boolean> {
    return this.sendVerificationCode(email, 'login');
  }

  async loginWithEmailCode(email: string, code: string): Promise<any> {
    const response = await request(`${this.apiBaseUrl}/auth/login-with-code`, {
      method: 'POST',
      body: JSON.stringify({ identifier: email, code }),
    });
    if (!response.ok) {
      const text = await response.text();
      throw new Error(text || '登录失败');
    }
    const payload = await response.json();
    return payload;
  }

  // 登出
  async logout(): Promise<void> {
    const response = await request(`${this.apiBaseUrl}/auth/logout`, {
      method: 'POST',
    });
    if (!response.ok) {
      const text = await response.text();
      console.warn('后端登出返回异常:', text);
    }
  }

  // 获取当前用户信息
  async getProfile(): Promise<any> {
    try {
      const response = await request(`${this.apiBaseUrl}/users/profile`, {
        method: 'GET',
      });
      if (!response.ok) {
        const text = await response.text();
        throw new Error(text || '获取用户信息失败');
      }
      const payload = await response.json();
      return payload; // expect user object
    } catch (error) {
      console.warn('Failed to fetch user profile from server, using fallback data:', error);
      // Fallback user data for development
      return {
        id: 'dev-user-1',
        email: '<EMAIL>',
        name: '开发用户',
        authProvider: 'email',
        createdAt: new Date(),
        phone: undefined,
        gender: 'other',
        birthday: new Date('1990-01-01'),
        height: 170,
        targetWeight: 65,
        activityLevel: 'moderately_active',
        preferences: {
          units: 'metric',
          language: 'zh',
          notifications: {
            dataReminder: true,
            goalAchievement: true,
            suggestions: true,
          },
        },
      };
    }
  }
}

export default AuthService.getInstance();