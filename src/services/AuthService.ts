import * as AuthSession from 'expo-auth-session';
import * as <PERSON><PERSON>rows<PERSON> from 'expo-web-browser';
import * as Crypto from 'expo-crypto';
import { User } from '../types';
import { request } from '../utils/request';

// Ensure WebBrowser can complete auth session
WebBrowser.maybeCompleteAuthSession();

// Optional Apple authentication
let AppleAuthentication: any = null;
try {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  AppleAuthentication = require('expo-apple-authentication');
} catch (err) {
  AppleAuthentication = null;
}

class AuthService {
  private static instance: AuthService;
  private apiBaseUrl = (require('../config').Config.api.baseUrl) as string;

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }



  // 邮箱登录
  async loginWithEmail(email: string, password: string): Promise<any> {
    const response = await request(`${this.apiBaseUrl}/auth/login`, {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
    if (!response.ok) {
      const text = await response.text();
      throw new Error(text || '登录失败');
    }
    const payload = await response.json();
    // 提取data字段中的实际数据
    return payload.data || payload; // { user, access_token }
  }

  // 注册
  async registerWithEmail(email: string, password: string, name: string): Promise<any> {
    const response = await request(`${this.apiBaseUrl}/auth/register`, {
      method: 'POST',
      body: JSON.stringify({ email, password, nickname: name }),
    });
    if (!response.ok) {
      const text = await response.text();
      throw new Error(text || '注册失败');
    }
    const payload = await response.json();
    // 提取data字段中的实际数据
    return payload.data || payload; // { user, access_token }
  }

  // 验证码注册
  async registerWithCode(email: string, code: string): Promise<any> {
    const response = await request(`${this.apiBaseUrl}/auth/register-with-code`, {
      method: 'POST',
      body: JSON.stringify({ identifier: email, code }),
    });
    if (!response.ok) {
      const text = await response.text();
      throw new Error(text || '注册失败');
    }
    const payload = await response.json();
    // 提取data字段中的实际数据
    return payload.data || payload; // { user, access_token }
  }

  // Google login placeholder (component side handles OAuth flow)
  async loginWithGoogle(): Promise<any> {
    throw new Error('请在组件端使用 OAuth 流程进行 Google 登录');
  }

  // Apple 登录（优先使用 expo-apple-authentication，若不可用则返回模拟）
  async loginWithApple(): Promise<any> {
    if (AppleAuthentication && AppleAuthentication.signInAsync) {
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });
      if (!credential || !credential.identityToken) {
        throw new Error('Apple 登录未返回 identityToken');
      }
      const response = await request(`${this.apiBaseUrl}/auth/social-login`, {
        method: 'POST',
        body: JSON.stringify({ accessToken: credential.identityToken, provider: 'apple' }),
      });
      if (!response.ok) {
        const text = await response.text();
        throw new Error(text || 'Apple 登录失败');
      }
      const payload = await response.json();
      // 提取data字段中的实际数据
      return payload.data || payload;
    }

    // Fallback for dev
    return {
      user: {
        id: '1',
        email: '<EMAIL>',
        name: 'Apple User',
        authProvider: 'apple',
        createdAt: new Date(),
      },
      access_token: null,
    };
  }

  // 社交登录（将第三方 access token 发给后端）
  async socialLogin(accessToken: string, provider: 'google' | 'apple'): Promise<any> {
    const response = await request(`${this.apiBaseUrl}/auth/social-login`, {
      method: 'POST',
      body: JSON.stringify({ accessToken, provider }),
    });
    if (!response.ok) {
      const text = await response.text();
      throw new Error(text || '社交登录失败');
    }
    const payload = await response.json();
    return payload;
  }

  // 手机号登录
  async loginWithPhone(phone: string, verificationCode: string): Promise<any> {
    const response = await request(`${this.apiBaseUrl}/auth/phone`, {
      method: 'POST',
      body: JSON.stringify({ phone, verificationCode }),
    });
    if (!response.ok) {
      const text = await response.text();
      throw new Error(text || '手机验证失败');
    }
    const userData = await response.json();
    return userData;
  }

  // 发送验证码
  async sendVerificationCode(identifier: string, type: 'login' | 'register' | 'reset_password' = 'login'): Promise<boolean> {
    const response = await request(`${this.apiBaseUrl}/auth/send-verification-code`, {
      method: 'POST',
      body: JSON.stringify({ identifier, type }),
    });
    if (!response.ok) {
      const text = await response.text();
      throw new Error(text || '发送验证码失败');
    }
    return true;
  }

  // Compatibility helper used by authSlice
  async sendEmailCode(email: string): Promise<boolean> {
    return this.sendVerificationCode(email, 'login');
  }

  async loginWithEmailCode(email: string, code: string): Promise<any> {
    const response = await request(`${this.apiBaseUrl}/auth/login-with-code`, {
      method: 'POST',
      body: JSON.stringify({ identifier: email, code }),
    });
    if (!response.ok) {
      const text = await response.text();
      throw new Error(text || '登录失败');
    }
    const payload = await response.json();
    // 提取data字段中的实际数据
    return payload.data || payload;
  }

  // 登出
  async logout(): Promise<void> {
    try {
      const response = await request(`${this.apiBaseUrl}/auth/logout`, {
        method: 'POST',
      });
      if (!response.ok) {
        const text = await response.text();
        console.warn('后端登出返回异常:', text);
      }
    } catch (error) {
      // 即使服务端登出失败，也要清除本地状态
      console.warn('登出请求失败，但将继续清除本地状态:', error);
    }
  }

  // 验证token有效性
  async validateToken(): Promise<{ valid: boolean; user?: any }> {
    try {
      const response = await request(`${this.apiBaseUrl}/auth/validate`, {
        method: 'GET',
      });

      if (response.ok) {
        const data = await response.json();
        const userData = data.data?.user || data.user;

        return {
          valid: true,
          user: userData
        };
      } else {
        console.warn('Token validation failed:', response.status, response.statusText);
        return { valid: false };
      }
    } catch (error) {
      console.warn('Token validation failed:', error);
      return { valid: false };
    }
  }

  // 刷新token
  async refreshToken(): Promise<any> {
    try {
      const response = await request(`${this.apiBaseUrl}/auth/refresh`, {
        method: 'POST',
      });
      if (!response.ok) {
        throw new Error('Token refresh failed');
      }
      const payload = await response.json();
      // 提取data字段中的实际数据
      return payload.data || payload; // { access_token, user }
    } catch (error) {
      console.warn('Token refresh failed:', error);
      throw error;
    }
  }

  // 获取当前用户信息
  async getProfile(): Promise<any> {
    try {
      const response = await request(`${this.apiBaseUrl}/users/profile`, {
        method: 'GET',
      });
      if (!response.ok) {
        const text = await response.text();
        throw new Error(text || '获取用户信息失败');
      }
      const payload = await response.json();
      // 提取data字段中的实际数据，对于profile接口，用户数据可能在data.user中
      return payload.data?.user || payload.data || payload; // expect user object
    } catch (error) {
      console.warn('Failed to fetch user profile from server, using fallback data:', error);
      // Fallback user data for development
      return {
        _id: 'dev-user-1',
        email: '<EMAIL>',
        nickname: '开发用户',
        authProvider: 'email',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastLoginAt: new Date(),
        phone: undefined,
        profile: {
          gender: 'other',
          birthday: new Date('1990-01-01'),
          height: 170,
          targetWeight: 65,
          activityLevel: 'moderately_active',
        },
        preferences: {
          units: 'metric',
          language: 'zh',
          notifications: {
            dataReminder: true,
            goalAchievement: true,
            suggestions: true,
          },
        },
      };
    }
  }
}

export default AuthService.getInstance();