import axios from 'axios';
import { HealthSuggestion, BodyCompositionData } from '../types';
import { Config, DEBUG } from '../config';

class ChatGPTService {
  private static instance: ChatGPTService;
  private apiKey: string = Config.openai.apiKey;
  private apiBaseUrl: string = Config.openai.baseUrl;
  private model: string = Config.openai.model;

  public static getInstance(): ChatGPTService {
    if (!ChatGPTService.instance) {
      ChatGPTService.instance = new ChatGPTService();
    }
    return ChatGPTService.instance;
  }

  // 生成健康建议
  async generateHealthSuggestions(
    userId: string, 
    bodyData: BodyCompositionData[]
  ): Promise<HealthSuggestion[]> {
    // 如果没有配置API密钥，返回备用建议
    if (!this.apiKey || this.apiKey === 'your-openai-api-key-here') {
      if (DEBUG.chatgpt) {
        console.log('使用备用建议，因为未配置OpenAI API密钥');
      }
      return this.getFallbackSuggestions(userId, bodyData);
    }

    try {
      const prompt = this.buildHealthPrompt(bodyData);
      
      const response = await axios.post(
        `${this.apiBaseUrl}/chat/completions`,
        {
          model: this.model,
          messages: [
            {
              role: 'system',
              content: '你是专业健康顾问。基于体脂数据提供3-5条具体可行的建议，包含饮食、运动、生活方式建议。每条建议应该具体、可操作、有针对性。'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 800,
          temperature: 0.7,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30秒超时
        }
      );

      const content = response.data.choices[0].message.content;
      if (DEBUG.chatgpt) {
        console.log('ChatGPT响应:', content);
      }
      
      return this.parseAISuggestions(userId, content);
    } catch (error: any) {
      console.error('生成AI建议失败:', error);
      
      // API失败时返回备用建议
      if (error.response?.status === 401) {
        throw new Error('OpenAI API密钥无效，请检查配置');
      } else if (error.response?.status === 429) {
        throw new Error('API请求过于频繁，请稍后再试');
      } else if (error.code === 'ENOTFOUND' || error.code === 'NETWORK_ERROR') {
        throw new Error('网络连接失败，请检查网络设置');
      }
      
      return this.getFallbackSuggestions(userId, bodyData);
    }
  }

  // 构建健康提示词
  private buildHealthPrompt(bodyData: BodyCompositionData[]): string {
    if (bodyData.length === 0) {
      return '用户刚开始使用应用，请提供一般性的健康生活建议。';
    }

    const latestData = bodyData[0];
    return `用户体脂数据：体重${latestData.weight}kg，体脂率${latestData.bodyFatPercentage}%，肌肉量${latestData.muscleMass}kg，BMI${latestData.bmi}。请提供专业健康建议。`;
  }

  // 解析AI建议
  private parseAISuggestions(userId: string, content: string): HealthSuggestion[] {
    const suggestions = content.split('\n').filter(line => line.trim().length > 20);
    
    return suggestions.slice(0, 5).map((content, index) => ({
      id: `ai_${Date.now()}_${index}`,
      userId,
      content: content.trim(),
      category: this.categorizeContent(content),
      createdAt: new Date(),
      basedOnData: ['latest_body_composition'],
    }));
  }

  // 内容分类
  private categorizeContent(content: string): 'diet' | 'exercise' | 'lifestyle' | 'general' {
    const lower = content.toLowerCase();
    if (lower.includes('饮食') || lower.includes('营养')) return 'diet';
    if (lower.includes('运动') || lower.includes('锻炼')) return 'exercise';
    if (lower.includes('睡眠') || lower.includes('生活')) return 'lifestyle';
    return 'general';
  }

  // 备用建议
  private getFallbackSuggestions(userId: string, bodyData: BodyCompositionData[]): HealthSuggestion[] {
    const suggestions = [
      {
        id: `fallback_${Date.now()}_1`,
        userId,
        content: '建议每周进行3-4次有氧运动，如快走、游泳或骑行，每次30-45分钟。',
        category: 'exercise' as const,
        createdAt: new Date(),
        basedOnData: ['general'],
      },
      {
        id: `fallback_${Date.now()}_2`,
        userId,
        content: '保持均衡饮食，多吃蔬菜水果，适量摄入优质蛋白质，控制精制糖分摄入。',
        category: 'diet' as const,
        createdAt: new Date(),
        basedOnData: ['general'],
      },
      {
        id: `fallback_${Date.now()}_3`,
        userId,
        content: '保持规律作息，每晚7-9小时优质睡眠，有助于身体恢复和新陈代谢。',
        category: 'lifestyle' as const,
        createdAt: new Date(),
        basedOnData: ['general'],
      },
    ];

    return suggestions;
  }
}

export default ChatGPTService.getInstance();