import { PermissionsAndroid, Platform, Alert } from 'react-native';

class PermissionService {
  private static instance: PermissionService;

  public static getInstance(): PermissionService {
    if (!PermissionService.instance) {
      PermissionService.instance = new PermissionService();
    }
    return PermissionService.instance;
  }

  // 请求蓝牙权限
  async requestBluetoothPermissions(): Promise<boolean> {
    if (Platform.OS === 'android') {
      return await this.requestAndroidBluetoothPermissions();
    } else {
      // iOS 蓝牙权限在 Info.plist 中配置，运行时自动请求
      return true;
    }
  }

  // Android 蓝牙权限请求
  private async requestAndroidBluetoothPermissions(): Promise<boolean> {
    try {
      const permissions = [
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      ];

      const granted = await PermissionsAndroid.requestMultiple(permissions);

      const allPermissionsGranted = permissions.every(
        permission => granted[permission] === PermissionsAndroid.RESULTS.GRANTED
      );

      if (!allPermissionsGranted) {
        Alert.alert(
          '权限被拒绝',
          '应用需要蓝牙和位置权限才能扫描和连接设备。请在设置中手动开启权限。',
          [
            { text: '取消', style: 'cancel' },
            { text: '去设置', onPress: () => this.openAppSettings() },
          ]
        );
        return false;
      }

      return true;
    } catch (error) {
      console.error('请求蓝牙权限失败:', error);
      return false;
    }
  }

  // 检查蓝牙权限
  async checkBluetoothPermissions(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const permissions = [
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ];

        const results = await Promise.all(
          permissions.map(permission => PermissionsAndroid.check(permission))
        );

        return results.every(result => result);
      } catch (error) {
        console.error('检查蓝牙权限失败:', error);
        return false;
      }
    }
    return true;
  }

  // 打开应用设置
  private openAppSettings(): void {
    // 这里需要使用第三方库如 react-native-app-settings
    // 或者 Linking.openSettings() (仅iOS)
    console.log('打开应用设置');
  }

  // 请求位置权限（某些设备扫描蓝牙需要）
  async requestLocationPermission(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: '位置权限',
            message: '应用需要位置权限来扫描蓝牙设备',
            buttonNeutral: '稍后询问',
            buttonNegative: '拒绝',
            buttonPositive: '同意',
          }
        );

        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (error) {
        console.error('请求位置权限失败:', error);
        return false;
      }
    }
    return true;
  }
}

export default PermissionService.getInstance();