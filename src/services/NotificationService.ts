import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';

// 配置通知处理器
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: false,
    shouldShowList: false,
  }),
});

class NotificationService {
  private static instance: NotificationService;
  private pushToken: string | null = null;

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // 注册推送通知权限
  async registerForPushNotifications(): Promise<string | null> {
    if (!Device.isDevice) {
      console.log('推送通知仅在真实设备上可用');
      return null;
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      throw new Error('推送通知权限被拒绝');
    }

    try {
      const projectId = Constants.expoConfig?.extra?.eas?.projectId ?? Constants.easConfig?.projectId;
      
      const token = await Notifications.getExpoPushTokenAsync({
        projectId,
      });
      
      this.pushToken = token.data;
      console.log('推送令牌:', this.pushToken);
      
      // 配置Android通知渠道
      if (Platform.OS === 'android') {
        await this.setupAndroidChannels();
      }
      
      return this.pushToken;
    } catch (error) {
      console.error('获取推送令牌失败:', error);
      return null;
    }
  }

  // 设置Android通知渠道
  private async setupAndroidChannels(): Promise<void> {
    await Notifications.setNotificationChannelAsync('health-reminders', {
      name: '健康提醒',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#6366f1',
      description: '体重测量和健康建议提醒',
    });

    await Notifications.setNotificationChannelAsync('data-updates', {
      name: '数据更新',
      importance: Notifications.AndroidImportance.DEFAULT,
      description: '体脂数据更新通知',
    });
  }

  // 立即发送本地通知
  async scheduleImmediateNotification(
    title: string,
    body: string,
    data?: any
  ): Promise<string> {
    return await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        sound: true,
      },
      trigger: null, // 立即发送
    });
  }

  // 定时发送通知
  async scheduleNotification(
    title: string,
    body: string,
    trigger: Date | Notifications.NotificationTriggerInput,
    data?: any
  ): Promise<string> {
    const triggerInput = trigger instanceof Date 
      ? { date: trigger, type: 'date' } as Notifications.DateTriggerInput
      : trigger;

    return await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        sound: true,
        categoryIdentifier: 'health-reminder',
      },
      trigger: triggerInput,
    });
  }

  // 设置每日提醒
  async scheduleDailyReminder(
    hour: number = 9,
    minute: number = 0,
    title: string = '记录今日体脂数据',
    body: string = '别忘记测量并记录您的身体数据，保持健康追踪习惯！'
  ): Promise<string> {
    // 取消之前的每日提醒
    await this.cancelNotificationsByIdentifier('daily-reminder');

    return await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data: { type: 'daily-reminder' },
        categoryIdentifier: 'health-reminder',
      },
      trigger: {
        hour,
        minute,
        repeats: true,
      } as Notifications.CalendarTriggerInput,
      identifier: 'daily-reminder',
    });
  }

  // 设置测量提醒
  async scheduleWeighingReminder(
    daysOfWeek: number[] = [1, 3, 5], // 周一、三、五
    hour: number = 8,
    minute: number = 0
  ): Promise<string[]> {
    const notificationIds: string[] = [];

    for (const dayOfWeek of daysOfWeek) {
      const id = await Notifications.scheduleNotificationAsync({
        content: {
          title: '体脂测量提醒',
          body: '该测量身体数据啦！连接设备开始记录吧。',
          data: { type: 'weighing-reminder', dayOfWeek },
          categoryIdentifier: 'health-reminder',
        },
        trigger: {
          weekday: dayOfWeek,
          hour,
          minute,
          repeats: true,
        } as Notifications.CalendarTriggerInput,
        identifier: `weighing-reminder-${dayOfWeek}`,
      });
      notificationIds.push(id);
    }

    return notificationIds;
  }

  // 发送AI建议通知
  async sendSuggestionNotification(suggestion: string): Promise<string> {
    return await this.scheduleImmediateNotification(
      '新的健康建议',
      suggestion,
      { type: 'ai-suggestion' }
    );
  }

  // 发送数据更新通知
  async sendDataUpdateNotification(
    weight: number,
    bodyFatPercentage: number
  ): Promise<string> {
    return await this.scheduleImmediateNotification(
      '数据已更新',
      `体重: ${weight.toFixed(1)}kg, 体脂率: ${bodyFatPercentage.toFixed(1)}%`,
      { type: 'data-update', weight, bodyFatPercentage }
    );
  }

  // 取消指定通知
  async cancelNotification(notificationId: string): Promise<void> {
    await Notifications.cancelScheduledNotificationAsync(notificationId);
  }

  // 根据标识符取消通知
  async cancelNotificationsByIdentifier(identifier: string): Promise<void> {
    const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
    const targetNotifications = scheduledNotifications.filter(
      notification => notification.identifier === identifier
    );

    for (const notification of targetNotifications) {
      await this.cancelNotification(notification.identifier);
    }
  }

  // 取消所有通知
  async cancelAllNotifications(): Promise<void> {
    await Notifications.cancelAllScheduledNotificationsAsync();
  }

  // 获取所有已安排的通知
  async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    return await Notifications.getAllScheduledNotificationsAsync();
  }

  // 设置通知监听器
  setupNotificationListeners(): {
    responseListener: Notifications.Subscription;
    receivedListener: Notifications.Subscription;
  } {
    // 监听通知响应（用户点击通知）
    const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      const data = response.notification.request.content.data;
      console.log('通知被点击:', data);
      
      // 根据通知类型处理导航
      this.handleNotificationResponse(data);
    });

    // 监听收到通知
    const receivedListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('收到通知:', notification);
    });

    return { responseListener, receivedListener };
  }

  // 处理通知响应
  private handleNotificationResponse(data: any): void {
    switch (data?.type) {
      case 'daily-reminder':
      case 'weighing-reminder':
        // 导航到数据录入页面
        console.log('导航到数据录入');
        break;
      case 'ai-suggestion':
        // 导航到建议页面
        console.log('导航到建议页面');
        break;
      case 'data-update':
        // 导航到数据页面
        console.log('导航到数据页面');
        break;
      default:
        console.log('未知通知类型');
    }
  }

  // 获取推送令牌
  getPushToken(): string | null {
    return this.pushToken;
  }

  // 清除应用角标
  async clearBadge(): Promise<void> {
    await Notifications.setBadgeCountAsync(0);
  }

  // 设置应用角标
  async setBadge(count: number): Promise<void> {
    await Notifications.setBadgeCountAsync(count);
  }
}

export default NotificationService.getInstance();