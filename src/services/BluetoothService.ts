import { Platform } from 'react-native';
import { BluetoothDevice, BodyCompositionData } from '../types';
import PermissionService from './PermissionService';

// 动态导入蓝牙库，仅在原生平台使用
let BleManager: any = null;
let Device: any = null;
let Characteristic: any = null;

if (Platform.OS !== 'web') {
  const bleModule = require('react-native-ble-plx');
  BleManager = bleModule.BleManager;
  Device = bleModule.Device;
  Characteristic = bleModule.Characteristic;
}

class BluetoothService {
  private static instance: BluetoothService;
  private manager: any = null;
  private connectedDevice: any = null;
  private isInitialized = false;
  private isWebPlatform: boolean;

  private constructor() {
    this.isWebPlatform = Platform.OS === 'web';
    if (!this.isWebPlatform && BleManager) {
      this.manager = new BleManager();
      this.initializeBluetooth();
    } else {
      console.warn('蓝牙功能在Web平台不可用，将使用模拟数据');
      this.isInitialized = true;
    }
  }

  public static getInstance(): BluetoothService {
    if (!BluetoothService.instance) {
      BluetoothService.instance = new BluetoothService();
    }
    return BluetoothService.instance;
  }

  // 初始化蓝牙
  private async initializeBluetooth(): Promise<void> {
    try {
      // 监听蓝牙状态变化
      this.manager.onStateChange((state: any) => {
        console.log('蓝牙状态变化:', state);
        if (state === 'PoweredOn') {
          this.isInitialized = true;
        } else {
          this.isInitialized = false;
        }
      }, true);
    } catch (error) {
      console.error('蓝牙初始化失败:', error);
    }
  }

  // 检查蓝牙是否可用
  private async checkBluetoothAvailable(): Promise<boolean> {
    if (this.isWebPlatform) {
      console.warn('Web平台不支持蓝牙功能');
      return false;
    }
    
    try {
      const state = await this.manager.state();
      if (state !== 'PoweredOn') {
        throw new Error('蓝牙未开启，请先开启蓝牙');
      }
      return true;
    } catch (error) {
      throw new Error('蓝牙不可用');
    }
  }

  // 扫描蓝牙设备
  async scanForDevices(): Promise<BluetoothDevice[]> {
    // Web平台返回模拟数据
    if (this.isWebPlatform) {
      console.log('在Web平台上返回模拟蓝牙设备数据');
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockDevices: BluetoothDevice[] = [
            {
              id: 'mock-scale-001',
              name: '智能体脂秤 Pro',
              type: 'body_scale',
              isConnected: false,
            },
            {
              id: 'mock-tracker-001',
              name: '健康手环',
              type: 'fitness_tracker',
              isConnected: false,
            }
          ];
          resolve(mockDevices);
        }, 2000); // 模拟扫描时间
      });
    }

    try {
      // 检查权限
      const hasPermissions = await PermissionService.requestBluetoothPermissions();
      if (!hasPermissions) {
        throw new Error('缺少蓝牙权限');
      }

      // 检查蓝牙状态
      await this.checkBluetoothAvailable();

      return new Promise((resolve, reject) => {
        const devices: BluetoothDevice[] = [];
        const deviceIds = new Set<string>();
        
        this.manager.startDeviceScan(null, null, (error: any, device: any) => {
          if (error) {
            console.error('扫描错误:', error);
            this.manager.stopDeviceScan();
            reject(new Error('扫描设备失败: ' + error.message));
            return;
          }

          if (device && device.name && !deviceIds.has(device.id)) {
            // 过滤健康相关设备
            if (this.isHealthDevice(device.name)) {
              deviceIds.add(device.id);
              const bluetoothDevice: BluetoothDevice = {
                id: device.id,
                name: device.name,
                type: this.getDeviceType(device.name),
                isConnected: false,
              };
              devices.push(bluetoothDevice);
            }
          }
        });

        // 10秒后停止扫描
        setTimeout(() => {
          this.manager.stopDeviceScan();
          resolve(devices);
        }, 10000);
      });
    } catch (error) {
      console.error('扫描设备失败:', error);
      throw error;
    }
  }

  // 判断是否为健康设备
  private isHealthDevice(deviceName: string): boolean {
    const healthKeywords = ['scale', 'body', 'weight', 'fat', 'composition', '体脂', '体重', '秤'];
    return healthKeywords.some(keyword => 
      deviceName.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  // 获取设备类型
  private getDeviceType(deviceName: string): 'body_scale' | 'fitness_tracker' | 'other' {
    const scaleKeywords = ['scale', 'weight', 'body', 'fat', '体脂', '体重', '秤'];
    const trackerKeywords = ['tracker', 'fitness', 'watch', 'band'];

    if (scaleKeywords.some(keyword => deviceName.toLowerCase().includes(keyword.toLowerCase()))) {
      return 'body_scale';
    } else if (trackerKeywords.some(keyword => deviceName.toLowerCase().includes(keyword.toLowerCase()))) {
      return 'fitness_tracker';
    }
    return 'other';
  }

  // 连接设备
  async connectToDevice(deviceId: string): Promise<BluetoothDevice> {
    // Web平台模拟连接
    if (this.isWebPlatform) {
      console.log('在Web平台上模拟连接设备:', deviceId);
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockDevice: BluetoothDevice = {
            id: deviceId,
            name: deviceId.includes('scale') ? '智能体脂秤 Pro' : '健康手环',
            type: deviceId.includes('scale') ? 'body_scale' : 'fitness_tracker',
            isConnected: true,
            lastConnected: new Date(),
          };
          this.connectedDevice = { id: deviceId, name: mockDevice.name };
          resolve(mockDevice);
        }, 1000);
      });
    }

    try {
      const device = await this.manager.connectToDevice(deviceId);
      await device.discoverAllServicesAndCharacteristics();
      
      this.connectedDevice = device;

      // 设置断开连接监听器
      device.onDisconnected(() => {
        console.log('设备已断开连接');
        this.connectedDevice = null;
      });

      return {
        id: device.id,
        name: device.name || '未知设备',
        type: this.getDeviceType(device.name || ''),
        isConnected: true,
        lastConnected: new Date(),
      };
    } catch (error) {
      console.error('连接设备失败:', error);
      throw new Error('连接设备失败');
    }
  }

  // 断开设备连接
  async disconnectDevice(deviceId: string): Promise<void> {
    // Web平台模拟断开连接
    if (this.isWebPlatform) {
      console.log('在Web平台上模拟断开设备连接:', deviceId);
      this.connectedDevice = null;
      return;
    }

    try {
      if (this.connectedDevice && this.connectedDevice.id === deviceId) {
        await this.manager.cancelDeviceConnection(deviceId);
        this.connectedDevice = null;
      }
    } catch (error) {
      console.error('断开连接失败:', error);
      throw new Error('断开连接失败');
    }
  }

  // 监听体脂数据
  async startDataMonitoring(
    onDataReceived: (data: Partial<BodyCompositionData>) => void
  ): Promise<void> {
    // Web平台生成模拟数据
    if (this.isWebPlatform) {
      console.log('在Web平台上生成模拟体脂数据');
      const generateMockData = () => {
        const mockData: Partial<BodyCompositionData> = {
          timestamp: new Date(),
          deviceId: this.connectedDevice?.id || 'mock-device',
          weight: 65 + Math.random() * 10, // 65-75kg
          bodyFatPercentage: 15 + Math.random() * 10, // 15-25%
          muscleMass: 45 + Math.random() * 5, // 45-50kg
          bmi: 22 + Math.random() * 3, // 22-25
          bmr: 1200 + Math.random() * 300, // 1200-1500
        };
        onDataReceived(mockData);
      };
      
      // 每5秒生成一次模拟数据
      setInterval(generateMockData, 5000);
      // 立即生成一次数据
      generateMockData();
      return;
    }

    if (!this.connectedDevice) {
      throw new Error('没有连接的设备');
    }

    try {
      // 这里是体脂秤的通用UUID，实际使用时需要根据具体设备调整
      const serviceUUID = '0000181D-0000-1000-8000-00805F9B34FB'; // Body Composition Service
      const characteristicUUID = '00002A9C-0000-1000-8000-00805F9B34FB'; // Body Composition Measurement

      // 监听特征值变化
      this.connectedDevice.monitorCharacteristicForService(
        serviceUUID,
        characteristicUUID,
        (error: any, characteristic: any) => {
          if (error) {
            console.error('监听数据失败:', error);
            return;
          }

          if (characteristic?.value) {
            const data = this.parseBodyCompositionData(characteristic.value);
            onDataReceived(data);
          }
        }
      );
    } catch (error) {
      console.error('开始数据监听失败:', error);
      // 如果没有找到标准服务，尝试查找其他服务
      await this.fallbackDataMonitoring(onDataReceived);
    }
  }

  // 备用数据监听方法
  private async fallbackDataMonitoring(
    onDataReceived: (data: Partial<BodyCompositionData>) => void
  ): Promise<void> {
    if (!this.connectedDevice) return;

    try {
      const services = await this.connectedDevice.services();
      
      for (const service of services) {
        const characteristics = await service.characteristics();
        
        for (const characteristic of characteristics) {
          if (characteristic.isNotifiable || characteristic.isIndicatable) {
            characteristic.monitor((error: any, char: any) => {
              if (error) {
                console.error('特征值监听错误:', error);
                return;
              }

              if (char?.value) {
                try {
                  const data = this.parseBodyCompositionData(char.value);
                  onDataReceived(data);
                } catch (parseError) {
                  console.log('数据解析失败，可能不是体脂数据');
                }
              }
            });
          }
        }
      }
    } catch (error) {
      console.error('备用监听方法失败:', error);
    }
  }

  // 解析体脂数据
  private parseBodyCompositionData(base64Data: string): Partial<BodyCompositionData> {
    try {
      // 将base64数据转换为字节数组
      const binaryString = atob(base64Data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // 解析IEEE-11073标准的体脂数据
      // 这里是一个简化的解析示例，实际解析需要根据具体设备的数据格式
      const data: Partial<BodyCompositionData> = {
        timestamp: new Date(),
        deviceId: this.connectedDevice?.id || '',
      };

      // 解析体重 (假设在字节2-3位置，单位为0.01kg)
      if (bytes.length >= 4) {
        data.weight = ((bytes[3] << 8) | bytes[2]) * 0.01;
      }

      // 解析体脂率 (假设在字节4-5位置，单位为0.1%)
      if (bytes.length >= 6) {
        data.bodyFatPercentage = ((bytes[5] << 8) | bytes[4]) * 0.1;
      }

      // 解析肌肉量 (假设在字节6-7位置，单位为0.01kg)
      if (bytes.length >= 8) {
        data.muscleMass = ((bytes[7] << 8) | bytes[6]) * 0.01;
      }

      // 计算BMI（如果有身高信息的话，这里使用默认值170cm）
      if (data.weight) {
        const height = 1.7; // 默认身高170cm
        data.bmi = data.weight / (height * height);
      }

      // 估算基础代谢率（简化计算）
      if (data.weight && data.muscleMass) {
        data.bmr = data.weight * 22; // 简化计算
      }

      return data;
    } catch (error) {
      console.error('数据解析错误:', error);
      throw new Error('数据解析失败');
    }
  }

  // 停止数据监听
  async stopDataMonitoring(): Promise<void> {
    if (this.connectedDevice) {
      try {
        // 停止所有特征值监听
        const services = await this.connectedDevice.services();
        for (const service of services) {
          const characteristics = await service.characteristics();
          for (const characteristic of characteristics) {
            if (characteristic.isNotifiable || characteristic.isIndicatable) {
              // 注意：这里需要保存监听subscription的引用来正确停止
              console.log('停止监听特征值:', characteristic.uuid);
            }
          }
        }
      } catch (error) {
        console.error('停止监听失败:', error);
      }
    }
  }

  // 获取连接状态
  isConnected(): boolean {
    return this.connectedDevice !== null;
  }

  // 获取连接的设备
  getConnectedDevice(): any {
    return this.connectedDevice;
  }
}

export default BluetoothService.getInstance();