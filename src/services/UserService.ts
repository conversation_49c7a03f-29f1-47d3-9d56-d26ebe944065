import { User } from '../types';
import { Config } from '../config';

class UserService {
  private static instance: UserService;
  private apiBaseUrl = Config.api.baseUrl; // 使用统一的API地址配置
  
  public static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  // 更新用户资料
  async updateProfile(userId: string, updates: Partial<User>): Promise<User> {
    try {
      // 实际项目中这里会调用API
      const response = await fetch(`${this.apiBaseUrl}/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`, // 实际项目中需要获取认证token
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error('更新用户资料失败');
      }

      const updatedUser = await response.json();
      return updatedUser;
    } catch (error) {
      console.error('更新用户资料失败:', error);
      // 开发阶段返回模拟的更新结果
      // 在实际应用中，这里应该抛出错误
      throw new Error('更新用户资料失败');
    }
  }

  // 上传用户头像
  async uploadAvatar(userId: string, imageUri: string): Promise<string> {
    try {
      // 实际项目中会使用 FormData 上传图片文件
      const formData = new FormData();
      formData.append('avatar', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'avatar.jpg',
      } as any);

      const response = await fetch(`${this.apiBaseUrl}/users/${userId}/avatar`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('上传头像失败');
      }

      const result = await response.json();
      return result.avatarUrl;
    } catch (error) {
      console.error('上传头像失败:', error);
      throw new Error('上传头像失败');
    }
  }

  // 获取用户统计信息
  async getUserStats(userId: string): Promise<{
    totalMeasurements: number;
    consecutiveDays: number;
    achievedGoals: number;
    averageBMI: number;
  }> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/users/${userId}/stats`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error('获取用户统计失败');
      }

      return await response.json();
    } catch (error) {
      console.error('获取用户统计失败:', error);
      // 返回模拟数据
      return {
        totalMeasurements: 42,
        consecutiveDays: 7,
        achievedGoals: 3,
        averageBMI: 22.5,
      };
    }
  }

  // 获取认证token (实际项目中需要实现)
  private getAuthToken(): string {
    // 实际项目中从 AsyncStorage 或其他安全存储中获取
    return 'mock-auth-token';
  }

  // 验证用户输入数据
  validateUserInput(updates: Partial<User>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证身高
    if (updates.height !== undefined) {
      if (updates.height < 50 || updates.height > 300) {
        errors.push('身高必须在50-300cm之间');
      }
    }

    // 验证目标体重
    if (updates.targetWeight !== undefined) {
      if (updates.targetWeight < 20 || updates.targetWeight > 300) {
        errors.push('目标体重必须在20-300kg之间');
      }
    }

    // 验证生日
    if (updates.birthday !== undefined) {
      const today = new Date();
      const birthDate = new Date(updates.birthday);
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 13 || age > 120) {
        errors.push('年龄必须在13-120岁之间');
      }
    }

    // 验证姓名
    if (updates.name !== undefined) {
      if (updates.name.trim().length < 1 || updates.name.trim().length > 50) {
        errors.push('姓名长度必须在1-50个字符之间');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

export default UserService.getInstance();