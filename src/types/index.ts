// 用户相关类型
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  authProvider: 'google' | 'apple' | 'email' | 'phone';
  createdAt: Date;
  phone?: string;
  gender?: 'male' | 'female' | 'other';
  birthday?: Date;
  height?: number; // 身高 (cm)
  targetWeight?: number; // 目标体重 (kg)
  activityLevel?: 'sedentary' | 'lightly_active' | 'moderately_active' | 'very_active' | 'extremely_active';
  preferences?: {
    units?: 'metric' | 'imperial'; // 单位制
    language?: 'zh' | 'en'; // 语言
    notifications?: {
      dataReminder?: boolean; // 测量提醒
      goalAchievement?: boolean; // 目标达成提醒
      suggestions?: boolean; // 建议推送
    };
  };
}

// 体脂数据类型
export interface BodyCompositionData {
  id: string;
  userId: string;
  timestamp: Date;
  weight: number; // 体重 (kg)
  bodyFatPercentage: number; // 体脂率 (%)
  muscleMass: number; // 肌肉量 (kg)
  boneMass: number; // 骨量 (kg)
  waterPercentage: number; // 水分率 (%)
  visceralFat: number; // 内脏脂肪
  bmr: number; // 基础代谢率 (kcal)
  bmi: number; // BMI指数
  deviceId: string; // 设备ID
}

// 蓝牙设备类型
export interface BluetoothDevice {
  id: string;
  name: string;
  type: 'body_scale' | 'fitness_tracker' | 'other';
  isConnected: boolean;
  lastConnected?: Date;
}

// AI建议类型
export interface HealthSuggestion {
  id: string;
  userId: string;
  content: string;
  category: 'diet' | 'exercise' | 'lifestyle' | 'general';
  createdAt: Date;
  basedOnData: string[]; // 基于哪些数据生成的建议
}

// 图表数据类型
export interface ChartDataPoint {
  label: string;
  value: number;
  date: Date;
}

// 导航相关类型
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Login: undefined;
  Register: undefined;
  Home: undefined;
  Profile: undefined;
  Devices: undefined;
  Data: undefined;
  Suggestions: undefined;
};

// Redux状态类型
export interface RootState {
  auth: AuthState;
  bodyData: BodyDataState;
  devices: DevicesState;
  suggestions: SuggestionsState;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  language?: 'zh' | 'en';
  token?: string | null;
}

export interface BodyDataState {
  data: BodyCompositionData[];
  isLoading: boolean;
  error: string | null;
}

export interface DevicesState {
  connectedDevices: BluetoothDevice[];
  availableDevices: BluetoothDevice[];
  isScanning: boolean;
  error: string | null;
}

export interface SuggestionsState {
  suggestions: HealthSuggestion[];
  isLoading: boolean;
  error: string | null;
}