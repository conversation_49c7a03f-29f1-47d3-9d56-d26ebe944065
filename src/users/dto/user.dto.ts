import { IsEmail, IsString, IsOptional, MinLength, IsEnum, IsNumber, IsDateString, IsBoolean } from 'class-validator';
import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';

export class CreateUserDto {
  @ApiProperty({ description: '用户昵称', example: '健康达人' })
  @IsString({ message: '用户昵称必须是字符串' })
  @Transform(({ value }) => value.trim())
  nickname: string;

  @ApiProperty({ description: '邮箱地址', example: '<EMAIL>', required: false })
  @IsOptional()
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  email?: string;

  @ApiProperty({ description: '手机号码', example: '13800138000', required: false })
  @IsOptional()
  @IsString({ message: '手机号码必须是字符串' })
  phone?: string;

  @ApiProperty({ description: '密码', example: 'password123', required: false })
  @IsOptional()
  @IsString({ message: '密码必须是字符串' })
  @MinLength(6, { message: '密码长度至少6位' })
  password?: string;

  @ApiProperty({ description: '头像URL', required: false })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiProperty({ description: '是否激活', default: true, required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateUserDto extends PartialType(CreateUserDto) {
  @ApiProperty({ description: '用户设置', required: false })
  @IsOptional()
  settings?: {
    notifications?: {
      dailyReminder?: boolean;
      weighingReminder?: boolean;
      aiSuggestions?: boolean;
      dataUpdates?: boolean;
    };
    privacy?: {
      dataSharing?: boolean;
      analyticsOptOut?: boolean;
    };
    preferences?: {
      theme?: string;
      language?: string;
      heightUnit?: string;
      weightUnit?: string;
    };
  };

  @ApiProperty({ description: '个人信息', required: false })
  @IsOptional()
  profile?: {
    height?: number;
    gender?: string;
    birthday?: Date;
    activityLevel?: string;
  };

  @ApiProperty({ description: '用户状态', enum: ['active', 'inactive', 'suspended'], required: false })
  @IsOptional()
  @IsEnum(['active', 'inactive', 'suspended'])
  status?: string;
}

export class UserQueryDto {
  @ApiProperty({ description: '页码', default: 1, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '页码必须是数字' })
  page?: number = 1;

  @ApiProperty({ description: '每页数量', default: 10, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '每页数量必须是数字' })
  limit?: number = 10;

  @ApiProperty({ description: '搜索关键词', required: false })
  @IsOptional()
  @IsString()
  search?: string;
}

export class UserProfileDto {
  @ApiProperty({ description: '身高(cm)', required: false })
  @IsOptional()
  @IsNumber({}, { message: '身高必须是数字' })
  height?: number;

  @ApiProperty({ description: '性别', enum: ['male', 'female', 'other'], required: false })
  @IsOptional()
  @IsEnum(['male', 'female', 'other'])
  gender?: string;

  @ApiProperty({ description: '生日', required: false })
  @IsOptional()
  @IsDateString()
  birthday?: string;

  @ApiProperty({ description: '活动水平', enum: ['sedentary', 'light', 'moderate', 'active', 'very_active'], required: false })
  @IsOptional()
  @IsEnum(['sedentary', 'light', 'moderate', 'active', 'very_active'])
  activityLevel?: string;
}

export class UserSettingsDto {
  @ApiProperty({ description: '通知设置', required: false })
  @IsOptional()
  notifications?: {
    dailyReminder?: boolean;
    weighingReminder?: boolean;
    aiSuggestions?: boolean;
    dataUpdates?: boolean;
  };

  @ApiProperty({ description: '隐私设置', required: false })
  @IsOptional()
  privacy?: {
    dataSharing?: boolean;
    analyticsOptOut?: boolean;
  };

  @ApiProperty({ description: '偏好设置', required: false })
  @IsOptional()
  preferences?: {
    theme?: string;
    language?: string;
    heightUnit?: string;
    weightUnit?: string;
  };
}