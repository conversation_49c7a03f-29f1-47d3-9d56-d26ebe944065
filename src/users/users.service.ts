import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import * as bcrypt from 'bcryptjs';
import { User, UserDocument } from '../schemas/user.schema';
import { CreateUserDto, UpdateUserDto, UserQueryDto } from '@/users/dto/user.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const existingUser = await this.userModel.findOne({ 
      email: createUserDto.email 
    });
    
    if (existingUser) {
      throw new ConflictException('邮箱已被注册');
    }

    let passwordHash: string | undefined;
    if (createUserDto.password) {
      passwordHash = await bcrypt.hash(createUserDto.password, 12);
    }

    const newUser = new this.userModel({
      ...createUserDto,
      password: passwordHash,
    });

    return newUser.save();
  }

  async findAll(query: UserQueryDto) {
    const { page = 1, limit = 10, search } = query;
    const skip = (page - 1) * limit;

    const filter: any = {};
    if (search) {
      filter.$or = [
        { nickname: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    const [users, total] = await Promise.all([
      this.userModel
        .find(filter)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.userModel.countDocuments(filter),
    ]);

    return {
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findById(id: string): Promise<User | null> {
    if (!Types.ObjectId.isValid(id)) {
      return null;
    }
    return this.userModel.findById(id).exec();
  }

  async findOne(id: string): Promise<User> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('用户不存在');
    }
    
    const user = await this.userModel.findById(id).exec();
    if (!user) {
      throw new NotFoundException('用户不存在');
    }
    
    return user;
  }

  async findByEmail(email: string): Promise<UserDocument | null> {
    return this.userModel
      .findOne({ email: email.toLowerCase() })
      .select('+password') // 包含密码用于认证
      .exec();
  }

  async findByThirdParty(
    authProvider: string, 
    thirdPartyId: string
  ): Promise<UserDocument | null> {
    return this.userModel
      .findOne({ authProvider, thirdPartyId })
      .exec();
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('用户不存在');
    }

    // 如果更新密码，需要加密
    if (updateUserDto.password) {
      const hashedPassword = await bcrypt.hash(updateUserDto.password, 12);
      updateUserDto.password = hashedPassword;
    }

    const updatedUser = await this.userModel
      .findByIdAndUpdate(
        id, 
        { 
          ...updateUserDto,
          updatedAt: new Date()
        }, 
        { new: true }
      )
      .exec();

    if (!updatedUser) {
      throw new NotFoundException('用户不存在');
    }

    return updatedUser;
  }

  async remove(id: string): Promise<{ message: string }> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('用户不存在');
    }

    const deletedUser = await this.userModel.findByIdAndDelete(id).exec();
    
    if (!deletedUser) {
      throw new NotFoundException('用户不存在');
    }

    return { message: '用户已删除' };
  }

  async updateLastLoginAt(id: string): Promise<void> {
    await this.userModel
      .findByIdAndUpdate(id, { lastLoginAt: new Date() })
      .exec();
  }

  async getUserStats(userId: string) {
    if (!Types.ObjectId.isValid(userId)) {
      throw new NotFoundException('用户不存在');
    }

    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 这里可以添加更多统计逻辑
    // 比如关联查询体脂数据、设备数量等
    return {
      user: {
        id: user._id,
        nickname: user.nickname,
        email: user.email,
        avatar: user.avatar,
        joinedAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
      },
      stats: {
        // 这些数据需要从其他集合查询
        totalBodyDataRecords: 0,
        connectedDevices: 0,
        aiSuggestionsReceived: 0,
        accountAgeDays: Math.floor(
          (Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24)
        ),
      },
    };
  }

  async validatePassword(
    user: UserDocument, 
    password: string
  ): Promise<boolean> {
    if (!user.password) {
      return false;
    }
    return bcrypt.compare(password, user.password);
  }
}