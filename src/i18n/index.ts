import * as Localization from 'expo-localization';
import { I18n } from 'i18n-js';

// 导入翻译文件
import en from './locales/en.json';
import zh from './locales/zh.json';

// 创建I18n实例
const i18n = new I18n({
  en,
  zh,
});

// 设置默认语言
i18n.defaultLocale = 'zh';

// 根据系统语言设置当前语言
const systemLocale = Localization.getLocales()[0]?.languageTag || 'zh';
const languageCode = systemLocale.split('-')[0];

// 如果系统语言在我们的支持列表中，使用系统语言，否则使用默认语言
if (['en', 'zh'].includes(languageCode)) {
  i18n.locale = languageCode;
} else {
  i18n.locale = 'zh';
}

// 启用回退
i18n.enableFallback = true;

// 获取当前语言
export const getCurrentLanguage = () => i18n.locale;

// 设置语言
export const setLanguage = (language: 'en' | 'zh') => {
  i18n.locale = language;
};

// 翻译函数
export const t = (key: string, params?: Record<string, any>) => {
  return i18n.t(key, params);
};

// 获取支持的语言列表
export const supportedLanguages = [
  { code: 'zh', name: '简体中文' },
  { code: 'en', name: 'English' },
];

export default i18n;