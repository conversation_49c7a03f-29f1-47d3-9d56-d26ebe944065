import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useGoogleAuth } from '../hooks/useGoogleAuth';
import { useDispatch } from 'react-redux';
import { setUser, setToken } from '../store/slices/authSlice';
import { AppDispatch } from '../store';

export const GoogleLoginButton: React.FC = () => {
  const { loginWithGoogle, response, isLoading, result } = useGoogleAuth();
  const dispatch = useDispatch<AppDispatch>();

  const handleGoogleLogin = async () => {
    try {
      await loginWithGoogle();
    } catch (error) {
      Alert.alert('登录失败', '无法连接到Google服务');
    }
  };

  React.useEffect(() => {
    if (response?.type === 'success') {
      // 等待 hook.exchange 完成后，result 会被设置
      // 由下面的 effect 处理 result
    } else if (response?.type === 'error') {
      Alert.alert('登录失败', response.error?.message || '未知错误');
    }
  }, [response]);

  React.useEffect(() => {
    if (!result) return;
    if (result.error) {
      Alert.alert('登录失败', result.error);
      return;
    }

    // payload expected { user, access_token }
    const payload = result;
    if (payload.user && payload.access_token) {
      dispatch(setUser(payload.user));
      dispatch(setToken(payload.access_token));
      Alert.alert('登录成功', '欢迎回来！');
    } else {
      Alert.alert('登录失败', '未收到有效的登录信息');
    }
  }, [result]);

  return (
    <TouchableOpacity
      style={[styles.button, isLoading && styles.buttonDisabled]}
      onPress={handleGoogleLogin}
      disabled={isLoading}
    >
      <Text style={styles.buttonText}>
        {isLoading ? '正在准备...' : '使用Google登录'}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#4285f4',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default GoogleLoginButton;