import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  Modal,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { AppDispatch, RootState } from '../store';
import { addBodyData } from '../store/slices/bodyDataSlice';
import { BodyCompositionData } from '../types';

interface BodyDataInputModalProps {
  visible: boolean;
  onClose: () => void;
}

const BodyDataInputModal: React.FC<BodyDataInputModalProps> = ({ visible, onClose }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { isLoading } = useSelector((state: RootState) => state.bodyData);

  const [formData, setFormData] = useState({
    weight: '',
    bodyFatPercentage: '',
    muscleMass: '',
    boneMass: '',
    waterPercentage: '',
    visceralFat: '',
    bmr: '',
  });

  const handleSubmit = async () => {
    if (!user) {
      Alert.alert('错误', '用户未登录');
      return;
    }

    if (!formData.weight) {
      Alert.alert('错误', '请输入体重');
      return;
    }

    try {
      const weight = parseFloat(formData.weight);
      const bodyFatPercentage = formData.bodyFatPercentage ? parseFloat(formData.bodyFatPercentage) : 0;
      const muscleMass = formData.muscleMass ? parseFloat(formData.muscleMass) : 0;
      const boneMass = formData.boneMass ? parseFloat(formData.boneMass) : 0;
      const waterPercentage = formData.waterPercentage ? parseFloat(formData.waterPercentage) : 0;
      const visceralFat = formData.visceralFat ? parseFloat(formData.visceralFat) : 0;
      const bmr = formData.bmr ? parseFloat(formData.bmr) : 0;

      // 计算BMI (使用默认身高170cm)
      const height = 1.7;
      const bmi = weight / (height * height);

      const bodyData: Omit<BodyCompositionData, 'id'> = {
        userId: user.id,
        timestamp: new Date(),
        weight,
        bodyFatPercentage,
        muscleMass,
        boneMass,
        waterPercentage,
        visceralFat,
        bmr: bmr || weight * 22, // 如果未输入BMR，使用简单估算
        bmi,
        deviceId: 'manual_input',
      };

      await dispatch(addBodyData(bodyData)).unwrap();
      Alert.alert('成功', '数据已保存');
      handleReset();
      onClose();
    } catch (error) {
      Alert.alert('错误', '保存数据失败');
    }
  };

  const handleReset = () => {
    setFormData({
      weight: '',
      bodyFatPercentage: '',
      muscleMass: '',
      boneMass: '',
      waterPercentage: '',
      visceralFat: '',
      bmr: '',
    });
  };

  const InputField: React.FC<{
    label: string;
    value: string;
    onChangeText: (text: string) => void;
    placeholder: string;
    unit: string;
    required?: boolean;
  }> = ({ label, value, onChangeText, placeholder, unit, required = false }) => (
    <View style={styles.inputGroup}>
      <Text style={styles.label}>
        {label} {required && <Text style={styles.required}>*</Text>}
      </Text>
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.input}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          keyboardType="numeric"
          placeholderTextColor="#9ca3af"
        />
        <Text style={styles.unit}>{unit}</Text>
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#6b7280" />
          </TouchableOpacity>
          <Text style={styles.title}>手动录入数据</Text>
          <TouchableOpacity onPress={handleReset} style={styles.resetButton}>
            <Text style={styles.resetButtonText}>重置</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Text style={styles.description}>
            请输入您的体脂数据。标记为*的字段为必填项。
          </Text>

          <InputField
            label="体重"
            value={formData.weight}
            onChangeText={(text) => setFormData({ ...formData, weight: text })}
            placeholder="70.0"
            unit="kg"
            required
          />

          <InputField
            label="体脂率"
            value={formData.bodyFatPercentage}
            onChangeText={(text) => setFormData({ ...formData, bodyFatPercentage: text })}
            placeholder="18.5"
            unit="%"
          />

          <InputField
            label="肌肉量"
            value={formData.muscleMass}
            onChangeText={(text) => setFormData({ ...formData, muscleMass: text })}
            placeholder="32.0"
            unit="kg"
          />

          <InputField
            label="骨量"
            value={formData.boneMass}
            onChangeText={(text) => setFormData({ ...formData, boneMass: text })}
            placeholder="2.8"
            unit="kg"
          />

          <InputField
            label="水分率"
            value={formData.waterPercentage}
            onChangeText={(text) => setFormData({ ...formData, waterPercentage: text })}
            placeholder="60.0"
            unit="%"
          />

          <InputField
            label="内脏脂肪"
            value={formData.visceralFat}
            onChangeText={(text) => setFormData({ ...formData, visceralFat: text })}
            placeholder="5"
            unit=""
          />

          <InputField
            label="基础代谢率"
            value={formData.bmr}
            onChangeText={(text) => setFormData({ ...formData, bmr: text })}
            placeholder="1650"
            unit="kcal/天"
          />

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.submitButton, isLoading && styles.disabledButton]}
              onPress={handleSubmit}
              disabled={isLoading}
            >
              <Text style={styles.submitButtonText}>
                {isLoading ? '保存中...' : '保存数据'}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  resetButton: {
    padding: 8,
  },
  resetButtonText: {
    color: '#6366f1',
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  description: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 24,
    lineHeight: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  required: {
    color: '#ef4444',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  input: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 16,
    color: '#374151',
  },
  unit: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  buttonContainer: {
    marginTop: 32,
    marginBottom: 20,
  },
  submitButton: {
    backgroundColor: '#6366f1',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.6,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default BodyDataInputModal;