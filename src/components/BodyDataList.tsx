import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BodyCompositionData } from '../types';

interface BodyDataListProps {
  data: BodyCompositionData[];
  onItemPress?: (item: BodyCompositionData) => void;
  onDeleteItem?: (item: BodyCompositionData) => void;
}

const BodyDataList: React.FC<BodyDataListProps> = ({
  data,
  onItemPress,
  onDeleteItem,
}) => {
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatValue = (value: number, unit: string) => {
    return `${value.toFixed(1)}${unit}`;
  };

  const getBMICategory = (bmi: number) => {
    if (bmi < 18.5) return { text: '偏瘦', color: '#3b82f6' };
    if (bmi < 24) return { text: '正常', color: '#10b981' };
    if (bmi < 28) return { text: '超重', color: '#f59e0b' };
    return { text: '肥胖', color: '#ef4444' };
  };

  const handleDelete = (item: BodyCompositionData) => {
    Alert.alert(
      '删除确认',
      '确定要删除这条记录吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => onDeleteItem?.(item),
        },
      ]
    );
  };

  const renderItem = ({ item }: { item: BodyCompositionData }) => {
    const bmiCategory = getBMICategory(item.bmi);
    
    return (
      <TouchableOpacity
        style={styles.item}
        onPress={() => onItemPress?.(item)}
        activeOpacity={0.7}
      >
        <View style={styles.itemHeader}>
          <View style={styles.dateContainer}>
            <Ionicons name="time-outline" size={16} color="#6b7280" />
            <Text style={styles.date}>{formatDate(item.timestamp)}</Text>
          </View>
          {onDeleteItem && (
            <TouchableOpacity
              onPress={() => handleDelete(item)}
              style={styles.deleteButton}
            >
              <Ionicons name="trash-outline" size={16} color="#ef4444" />
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.dataGrid}>
          <View style={styles.dataItem}>
            <Text style={styles.dataLabel}>体重</Text>
            <Text style={styles.dataValue}>{formatValue(item.weight, 'kg')}</Text>
          </View>

          <View style={styles.dataItem}>
            <Text style={styles.dataLabel}>体脂率</Text>
            <Text style={styles.dataValue}>{formatValue(item.bodyFatPercentage, '%')}</Text>
          </View>

          <View style={styles.dataItem}>
            <Text style={styles.dataLabel}>肌肉量</Text>
            <Text style={styles.dataValue}>{formatValue(item.muscleMass, 'kg')}</Text>
          </View>

          <View style={styles.dataItem}>
            <Text style={styles.dataLabel}>BMI</Text>
            <View style={styles.bmiContainer}>
              <Text style={styles.dataValue}>{item.bmi.toFixed(1)}</Text>
              <View style={[styles.bmiBadge, { backgroundColor: bmiCategory.color }]}>
                <Text style={styles.bmiText}>{bmiCategory.text}</Text>
              </View>
            </View>
          </View>
        </View>

        {item.deviceId !== 'manual_input' && (
          <View style={styles.deviceInfo}>
            <Ionicons name="bluetooth" size={14} color="#6366f1" />
            <Text style={styles.deviceText}>设备数据</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="analytics-outline" size={64} color="#9ca3af" />
      <Text style={styles.emptyTitle}>暂无数据</Text>
      <Text style={styles.emptySubtitle}>
        连接设备或手动录入数据开始记录您的健康状况
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={data}
        keyExtractor={(item) => item.id}
        renderItem={renderItem}
        contentContainerStyle={[
          styles.listContent,
          data.length === 0 && styles.emptyListContent,
        ]}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  emptyListContent: {
    flex: 1,
    justifyContent: 'center',
  },
  item: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  date: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 6,
    fontWeight: '500',
  },
  deleteButton: {
    padding: 4,
  },
  dataGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  dataItem: {
    width: '48%',
    marginBottom: 12,
  },
  dataLabel: {
    fontSize: 12,
    color: '#9ca3af',
    marginBottom: 4,
  },
  dataValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  bmiContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bmiBadge: {
    marginLeft: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  bmiText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: '500',
  },
  deviceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  deviceText: {
    fontSize: 12,
    color: '#6366f1',
    marginLeft: 4,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 32,
  },
});

export default BodyDataList;