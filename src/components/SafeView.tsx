import React from 'react';
import { View, ViewProps, StyleProp, ViewStyle } from 'react-native';

type Props = ViewProps & {
  // some callers might pass deprecated props.pointerEvents
  pointerEventsProp?: ViewProps['pointerEvents'];
};

export default function SafeView(props: Props) {
  const { style, pointerEvents: pointerEventsStyle, ...rest } = props as any;

  // If someone passed pointerEvents as a prop (deprecated), move it into style
  const finalStyle: StyleProp<ViewStyle> = Array.isArray(style) ? style : [style];
  if (rest.pointerEvents) {
    // ensure style has pointerEvents
    finalStyle.push({ pointerEvents: rest.pointerEvents });
    // remove deprecated prop to avoid warnings in children
    delete rest.pointerEvents;
  }

  return <View {...rest} style={finalStyle} pointerEvents={pointerEventsStyle} />;
}
