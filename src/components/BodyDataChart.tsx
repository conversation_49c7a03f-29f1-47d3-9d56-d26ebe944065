import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import { Ionicons } from '@expo/vector-icons';
import { BodyCompositionData, ChartDataPoint } from '../types';

interface BodyDataChartProps {
  data: BodyCompositionData[];
}

type ChartType = 'weight' | 'bodyFat' | 'muscle' | 'bmi';

const BodyDataChart: React.FC<BodyDataChartProps> = ({ data }) => {
  const [selectedChart, setSelectedChart] = useState<ChartType>('weight');
  const screenWidth = Dimensions.get('window').width;

  // 处理数据为图表格式
  const processChartData = (type: ChartType): ChartDataPoint[] => {
    if (data.length === 0) return [];

    const sortedData = [...data]
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
      .slice(-30); // 最近30条数据

    return sortedData.map((item, index) => {
      let value: number;
      switch (type) {
        case 'weight':
          value = item.weight;
          break;
        case 'bodyFat':
          value = item.bodyFatPercentage;
          break;
        case 'muscle':
          value = item.muscleMass;
          break;
        case 'bmi':
          value = item.bmi;
          break;
        default:
          value = 0;
      }

      return {
        label: new Date(item.timestamp).toLocaleDateString('zh-CN', {
          month: 'short',
          day: 'numeric',
        }),
        value,
        date: new Date(item.timestamp),
      };
    });
  };

  const getChartConfig = () => ({
    backgroundColor: '#ffffff',
    backgroundGradientFrom: '#ffffff',
    backgroundGradientTo: '#ffffff',
    decimalPlaces: 1,
    color: (opacity = 1) => getChartColor(opacity),
    labelColor: (opacity = 1) => `rgba(107, 114, 128, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '4',
      strokeWidth: '2',
      stroke: getChartColor(),
    },
    propsForBackgroundLines: {
      strokeDasharray: '',
      stroke: '#f3f4f6',
      strokeWidth: 1,
    },
  });

  const getChartColor = (opacity = 1) => {
    const colors = {
      weight: `rgba(99, 102, 241, ${opacity})`,
      bodyFat: `rgba(239, 68, 68, ${opacity})`,
      muscle: `rgba(16, 185, 129, ${opacity})`,
      bmi: `rgba(245, 158, 11, ${opacity})`,
    };
    return colors[selectedChart];
  };

  const getChartTitle = () => {
    const titles = {
      weight: '体重趋势',
      bodyFat: '体脂率趋势',
      muscle: '肌肉量趋势',
      bmi: 'BMI趋势',
    };
    return titles[selectedChart];
  };

  const getChartUnit = () => {
    const units = {
      weight: 'kg',
      bodyFat: '%',
      muscle: 'kg',
      bmi: '',
    };
    return units[selectedChart];
  };

  const chartData = processChartData(selectedChart);

  // 准备图表数据格式
  const chartConfig = {
    labels: chartData.slice(-7).map(item => item.label), // 显示最近7个数据点的标签
    datasets: [
      {
        data: chartData.slice(-7).map(item => item.value),
        color: (opacity = 1) => getChartColor(opacity),
        strokeWidth: 3,
      },
    ],
  };

  const calculateTrend = () => {
    if (chartData.length < 2) return null;
    
    const recent = chartData.slice(-7);
    const first = recent[0]?.value || 0;
    const last = recent[recent.length - 1]?.value || 0;
    const change = last - first;
    const changePercent = first !== 0 ? (change / first) * 100 : 0;

    return {
      change,
      changePercent,
      isPositive: change > 0,
      isSignificant: Math.abs(changePercent) > 1,
    };
  };

  const trend = calculateTrend();

  const ChartSelector: React.FC = () => (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false}
      style={styles.selectorContainer}
    >
      {(['weight', 'bodyFat', 'muscle', 'bmi'] as ChartType[]).map((type) => (
        <TouchableOpacity
          key={type}
          style={[
            styles.selectorButton,
            selectedChart === type && styles.selectedButton,
          ]}
          onPress={() => setSelectedChart(type)}
        >
          <Text
            style={[
              styles.selectorText,
              selectedChart === type && styles.selectedText,
            ]}
          >
            {getChartTitle().replace('趋势', '')}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  if (data.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyContainer}>
          <Ionicons name="analytics-outline" size={48} color="#9ca3af" />
          <Text style={styles.emptyTitle}>暂无数据</Text>
          <Text style={styles.emptySubtitle}>添加体脂数据后即可查看趋势图</Text>
        </View>
      </View>
    );
  }

  if (chartData.length < 2) {
    return (
      <View style={styles.container}>
        <ChartSelector />
        <View style={styles.emptyContainer}>
          <Ionicons name="trending-up-outline" size={48} color="#9ca3af" />
          <Text style={styles.emptyTitle}>数据不足</Text>
          <Text style={styles.emptySubtitle}>至少需要2条数据才能显示趋势</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ChartSelector />
      
      <View style={styles.chartHeader}>
        <Text style={styles.chartTitle}>{getChartTitle()}</Text>
        {trend && (
          <View style={styles.trendContainer}>
            <Ionicons
              name={trend.isPositive ? 'trending-up' : 'trending-down'}
              size={16}
              color={trend.isPositive ? '#10b981' : '#ef4444'}
            />
            <Text
              style={[
                styles.trendText,
                { color: trend.isPositive ? '#10b981' : '#ef4444' },
              ]}
            >
              {trend.isPositive ? '+' : ''}{trend.change.toFixed(1)}{getChartUnit()}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.chartContainer}>
        <LineChart
          data={chartConfig}
          width={screenWidth - 40}
          height={220}
          chartConfig={getChartConfig()}
          bezier
          style={styles.chart}
          withInnerLines={true}
          withOuterLines={false}
          withVerticalLines={false}
          withHorizontalLines={true}
          withDots={true}
          withShadow={false}
          yAxisSuffix={getChartUnit()}
          yAxisInterval={1}
          formatYLabel={(value) => {
            const num = parseFloat(value);
            return num.toFixed(1);
          }}
        />
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>最高值</Text>
          <Text style={styles.statValue}>
            {Math.max(...chartData.map(d => d.value)).toFixed(1)}{getChartUnit()}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>最低值</Text>
          <Text style={styles.statValue}>
            {Math.min(...chartData.map(d => d.value)).toFixed(1)}{getChartUnit()}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>平均值</Text>
          <Text style={styles.statValue}>
            {(chartData.reduce((sum, d) => sum + d.value, 0) / chartData.length).toFixed(1)}{getChartUnit()}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>数据点</Text>
          <Text style={styles.statValue}>{chartData.length}个</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 16,
    margin: 20,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  selectorContainer: {
    marginBottom: 20,
  },
  selectorButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    marginRight: 12,
  },
  selectedButton: {
    backgroundColor: '#6366f1',
  },
  selectorText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  selectedText: {
    color: '#fff',
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  chartContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  chart: {
    borderRadius: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    color: '#9ca3af',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginTop: 12,
    marginBottom: 4,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
});

export default BodyDataChart;