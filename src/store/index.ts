import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import bodyDataSlice from './slices/bodyDataSlice';
import devicesSlice from './slices/devicesSlice';
import suggestionsSlice from './slices/suggestionsSlice';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { combineReducers } from 'redux';

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['auth'], // persist auth which includes language and user prefs
};

const combined = combineReducers({
  auth: authSlice,
  bodyData: bodyDataSlice,
  devices: devicesSlice,
  suggestions: suggestionsSlice,
});

const persisted = persistReducer(persistConfig, combined);

export const store = configureStore({
  reducer: persisted,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

export const persistor = persistStore(store);
export type RootState = ReturnType<typeof combined>;
export type AppDispatch = typeof store.dispatch;