import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { BluetoothDevice, DevicesState } from '../../types';
import BluetoothService from '../../services/BluetoothService';

const initialState: DevicesState = {
  connectedDevices: [],
  availableDevices: [],
  isScanning: false,
  error: null,
};

export const scanForDevices = createAsyncThunk(
  'devices/scanForDevices',
  async () => {
    const devices = await BluetoothService.scanForDevices();
    return devices;
  }
);

export const connectToDevice = createAsyncThunk(
  'devices/connectToDevice',
  async (deviceId: string) => {
    const device = await BluetoothService.connectToDevice(deviceId);
    return device;
  }
);

export const disconnectDevice = createAsyncThunk(
  'devices/disconnectDevice',
  async (deviceId: string) => {
    await BluetoothService.disconnectDevice(deviceId);
    return deviceId;
  }
);

const devicesSlice = createSlice({
  name: 'devices',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    addAvailableDevice: (state, action: PayloadAction<BluetoothDevice>) => {
      const exists = state.availableDevices.find(device => device.id === action.payload.id);
      if (!exists) {
        state.availableDevices.push(action.payload);
      }
    },
    removeAvailableDevice: (state, action: PayloadAction<string>) => {
      state.availableDevices = state.availableDevices.filter(device => device.id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(scanForDevices.pending, (state) => {
        state.isScanning = true;
        state.error = null;
        state.availableDevices = [];
      })
      .addCase(scanForDevices.fulfilled, (state, action) => {
        state.isScanning = false;
        state.availableDevices = action.payload;
      })
      .addCase(scanForDevices.rejected, (state, action) => {
        state.isScanning = false;
        state.error = action.error.message || '扫描设备失败';
      })
      .addCase(connectToDevice.pending, (state) => {
        state.error = null;
      })
      .addCase(connectToDevice.fulfilled, (state, action) => {
        const connectedDevice = action.payload;
        state.connectedDevices.push(connectedDevice);
        state.availableDevices = state.availableDevices.filter(device => device.id !== connectedDevice.id);
      })
      .addCase(connectToDevice.rejected, (state, action) => {
        state.error = action.error.message || '连接设备失败';
      })
      .addCase(disconnectDevice.fulfilled, (state, action) => {
        const deviceId = action.payload;
        const disconnectedDevice = state.connectedDevices.find(device => device.id === deviceId);
        if (disconnectedDevice) {
          state.connectedDevices = state.connectedDevices.filter(device => device.id !== deviceId);
          state.availableDevices.push({ ...disconnectedDevice, isConnected: false });
        }
      });
  },
});

export const { clearError, addAvailableDevice, removeAvailableDevice } = devicesSlice.actions;
export default devicesSlice.reducer;