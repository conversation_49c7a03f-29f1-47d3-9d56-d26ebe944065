import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { HealthSuggestion, SuggestionsState } from '../../types';
import ChatGPTService from '../../services/ChatGPTService';

const initialState: SuggestionsState = {
  suggestions: [],
  isLoading: false,
  error: null,
};

export const generateSuggestions = createAsyncThunk(
  'suggestions/generateSuggestions',
  async ({ userId, bodyData }: { userId: string; bodyData: any }) => {
    const suggestions = await ChatGPTService.generateHealthSuggestions(userId, bodyData);
    return suggestions;
  }
);

export const fetchSuggestions = createAsyncThunk(
  'suggestions/fetchSuggestions',
  async (userId: string) => {
    // 这里可以从本地存储或服务器获取建议
    return [];
  }
);

const suggestionsSlice = createSlice({
  name: 'suggestions',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    addSuggestion: (state, action: PayloadAction<HealthSuggestion>) => {
      state.suggestions.unshift(action.payload);
    },
    removeSuggestion: (state, action: PayloadAction<string>) => {
      state.suggestions = state.suggestions.filter(suggestion => suggestion.id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(generateSuggestions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(generateSuggestions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.suggestions = [...action.payload, ...state.suggestions];
      })
      .addCase(generateSuggestions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || '生成建议失败';
      })
      .addCase(fetchSuggestions.fulfilled, (state, action) => {
        state.suggestions = action.payload;
      });
  },
});

export const { clearError, addSuggestion, removeSuggestion } = suggestionsSlice.actions;
export default suggestionsSlice.reducer;