import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { User, AuthState } from '../../types';
import AuthService from '../../services/AuthService';
import * as secureStore from '../../utils/storage';
import { getCurrentLanguage } from '../../i18n';

// 标准化后端/服务返回的认证负载
export interface AuthPayload {
  user: User;
  access_token?: string | null;
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  language: getCurrentLanguage() as 'zh' | 'en',
  token: null,
};

// 异步actions
export const loginWithEmail = createAsyncThunk<AuthPayload, { email: string; password: string }, { rejectValue: string }>(
  'auth/loginWithEmail',
  async ({ email, password }) => {
    const response = await AuthService.loginWithEmail(email, password);
    return response as AuthPayload; // expect { user, access_token }
  }
);

export const loginWithGoogle = createAsyncThunk<AuthPayload | User, void, { rejectValue: string }>(
  'auth/loginWithGoogle',
  async () => {
    const response = await AuthService.loginWithGoogle();
    // service should return { user, access_token } but normalize if it returns a User directly
    if (response && (response as any).user) return response as AuthPayload;
    return response as User;
  }
);

export const loginWithApple = createAsyncThunk<AuthPayload | User, void, { rejectValue: string }>(
  'auth/loginWithApple',
  async () => {
    const response = await AuthService.loginWithApple();
    if (response && (response as any).user) return response as AuthPayload;
    return response as User;
  }
);

export const sendEmailCode = createAsyncThunk<boolean, string, { rejectValue: string }>(
  'auth/sendEmailCode',
  async (email: string) => {
    const res = await AuthService.sendEmailCode(email);
    return res;
  }
);

export const loginWithEmailCode = createAsyncThunk<AuthPayload, { email: string; code: string }, { rejectValue: string }>(
  'auth/loginWithEmailCode',
  async ({ email, code }) => {
    const response = await AuthService.loginWithEmailCode(email, code);
    return response as AuthPayload; // expect { user, access_token }
  }
);

export const registerWithEmail = createAsyncThunk<AuthPayload, { email: string; password: string; name: string }, { rejectValue: string }>(
  'auth/registerWithEmail',
  async ({ email, password, name }) => {
    const response = await AuthService.registerWithEmail(email, password, name);
    return response as AuthPayload;
  }
);

export const registerWithCode = createAsyncThunk<AuthPayload, { email: string; code: string }, { rejectValue: string }>(
  'auth/registerWithCode',
  async ({ email, code }) => {
    const response = await AuthService.registerWithCode(email, code);
    await secureStore.saveToken(response.access_token);
    return response as AuthPayload;
  }
);

export const logout = createAsyncThunk('auth/logout', async () => {
  try {
    await AuthService.logout();
  } catch (error) {
    console.warn('Server logout failed, but continuing with local cleanup:', error);
  }
  // 确保删除本地token
  await secureStore.deleteToken();
});

export const fetchCurrentUser = createAsyncThunk<User, void, { rejectValue: string }>(
  'auth/fetchCurrentUser',
  async () => {
    const user = await AuthService.getProfile();
    return user as User;
  }
);

// 验证并自动登录
export const validateAndAutoLogin = createAsyncThunk<
  { user: User; token: string } | null,
  void,
  { rejectValue: string }
>(
  'auth/validateAndAutoLogin',
  async (_, { rejectWithValue }) => {
    try {
      // 1. 获取存储的token
      const storedToken = await secureStore.getToken();
      if (!storedToken) {
        return null; // 没有token，需要登录
      }

      // 2. 验证token有效性
      const validationResult = await AuthService.validateToken();
      if (!validationResult.valid) {
        // Token无效，尝试刷新
        try {
          const refreshResult = await AuthService.refreshToken();
          if (refreshResult?.access_token) {
            // 保存新token
            await secureStore.saveToken(refreshResult.access_token);
            return {
              user: refreshResult.user as User,
              token: refreshResult.access_token
            };
          }
        } catch (refreshError) {
          console.warn('Token refresh failed, clearing stored token');
          await secureStore.deleteToken();
          return null;
        }
      }

      // 3. Token有效，返回用户信息
      return {
        user: validationResult.user as User,
        token: storedToken
      };
    } catch (error) {
      console.error('Auto login failed:', error);
      // 清除无效token
      await secureStore.deleteToken();
      return rejectWithValue('自动登录失败');
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setToken: (state, action: PayloadAction<string | null>) => {
      state.token = action.payload;
      state.isAuthenticated = !!action.payload;
    },
    setLanguage: (state, action: PayloadAction<'zh' | 'en'>) => {
      state.language = action.payload;
      if (state.user) {
        state.user = {
          ...state.user,
          preferences: {
            ...state.user.preferences,
            language: action.payload,
          },
        };
      }
    },
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
    },
    updateUserProfile: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Login with email
      .addCase(loginWithEmail.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginWithEmail.fulfilled, (state, action) => {
        state.isLoading = false;
        const payload = action.payload as AuthPayload;
        if (!payload) return;
        state.user = payload.user;
        if (payload.access_token) {
          state.token = payload.access_token;
          secureStore.saveToken(payload.access_token);
        }
        state.isAuthenticated = true;
      })
      .addCase(loginWithEmail.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || '登录失败';
      })
      // Login with Google
      .addCase(loginWithGoogle.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginWithGoogle.fulfilled, (state, action) => {
        state.isLoading = false;
        const payload = action.payload as AuthPayload | User | undefined;
        if (!payload) return;
        if ((payload as AuthPayload).user) {
          const p = payload as AuthPayload;
          state.user = p.user;
          if (p.access_token) {
            state.token = p.access_token;
            secureStore.saveToken(p.access_token);
          }
        } else {
          state.user = payload as User;
        }
        state.isAuthenticated = true;
      })
      .addCase(loginWithGoogle.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Google登录失败';
      })
      // Login with Apple
      .addCase(loginWithApple.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginWithApple.fulfilled, (state, action) => {
        state.isLoading = false;
        const payload = action.payload as AuthPayload | User | undefined;
        if (!payload) return;
        if ((payload as AuthPayload).user) {
          const p = payload as AuthPayload;
          state.user = p.user;
          if (p.access_token) {
            state.token = p.access_token;
            secureStore.saveToken(p.access_token);
          }
        } else {
          state.user = payload as User;
        }
        state.isAuthenticated = true;
      })
      .addCase(loginWithApple.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Apple登录失败';
      })
      // Register
      .addCase(registerWithEmail.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerWithEmail.fulfilled, (state, action) => {
        state.isLoading = false;
        const payload = action.payload as AuthPayload | User | undefined;
        if (!payload) return;
        if ((payload as AuthPayload).user) {
          const p = payload as AuthPayload;
          state.user = p.user;
          if (p.access_token) {
            state.token = p.access_token;
            secureStore.saveToken(p.access_token);
          }
        } else {
          state.user = payload as User;
        }
        state.isAuthenticated = true;
      })
      .addCase(registerWithEmail.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || '注册失败';
      })
      .addCase(sendEmailCode.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(sendEmailCode.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(sendEmailCode.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || '发送验证码失败';
      })
      .addCase(loginWithEmailCode.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginWithEmailCode.fulfilled, (state, action) => {
        state.isLoading = false;
        const payload = action.payload as AuthPayload;
        if (!payload) return;
        state.user = payload.user;
        if (payload.access_token) {
          state.token = payload.access_token;
          secureStore.saveToken(payload.access_token);
        }
        state.isAuthenticated = true;
      })
      .addCase(loginWithEmailCode.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || '登录失败';
      })
      // Fetch current user after token restore
      .addCase(fetchCurrentUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
      })
      .addCase(fetchCurrentUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || '获取用户信息失败';
        state.user = null;
        state.isAuthenticated = false;
        state.token = null;
        // best-effort delete token
        secureStore.deleteToken();
      })
      // Validate and auto login
      .addCase(validateAndAutoLogin.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(validateAndAutoLogin.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload) {
          // 自动登录成功
          state.user = action.payload.user;
          state.token = action.payload.token;
          state.isAuthenticated = true;
          state.error = null;
        } else {
          // 没有有效token，需要手动登录
          state.user = null;
          state.token = null;
          state.isAuthenticated = false;
        }
      })
      .addCase(validateAndAutoLogin.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || '自动登录失败';
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
      })
      // Logout
      .addCase(logout.fulfilled, (state) => {
        state.user = null;
        state.isAuthenticated = false;
        state.token = null;
        state.error = null;
        // token已在thunk中删除，这里不需要重复删除
      })
      .addCase(logout.rejected, (state, action) => {
        // 即使logout失败，也要清除本地状态
        state.user = null;
        state.isAuthenticated = false;
        state.token = null;
        state.error = action.error.message || '登出失败';
        // 强制删除token
        secureStore.deleteToken();
      });
  },
});

export const { clearError, setUser, updateUserProfile, setLanguage } = authSlice.actions;
export const { setToken } = authSlice.actions;
export default authSlice.reducer;