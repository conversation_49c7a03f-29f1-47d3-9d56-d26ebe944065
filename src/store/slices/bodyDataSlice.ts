import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { BodyCompositionData, BodyDataState } from '../../types';
import BodyDataService from '../../services/BodyDataService';

const initialState: BodyDataState = {
  data: [],
  isLoading: false,
  error: null,
};

export const fetchBodyData = createAsyncThunk(
  'bodyData/fetchBodyData',
  async (userId: string) => {
    const response = await BodyDataService.getBodyData(userId);
    return response;
  }
);

export const addBodyData = createAsyncThunk(
  'bodyData/addBodyData',
  async (data: Omit<BodyCompositionData, 'id'>) => {
    const response = await BodyDataService.addBodyData(data);
    return response;
  }
);

const bodyDataSlice = createSlice({
  name: 'bodyData',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    addLocalData: (state, action: PayloadAction<BodyCompositionData>) => {
      state.data.push(action.payload);
      state.data.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchBodyData.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchBodyData.fulfilled, (state, action) => {
        state.isLoading = false;
        state.data = action.payload;
      })
      .addCase(fetchBodyData.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || '获取数据失败';
      })
      .addCase(addBodyData.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addBodyData.fulfilled, (state, action) => {
        state.isLoading = false;
        state.data.unshift(action.payload);
      })
      .addCase(addBodyData.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || '添加数据失败';
      });
  },
});

export const { clearError, addLocalData } = bodyDataSlice.actions;
export default bodyDataSlice.reducer;