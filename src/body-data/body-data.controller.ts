import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  UseGuards, 
  Request,
  Query,
  ParseIntPipe,
  DefaultValuePipe
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { BodyDataService } from './body-data.service';
import { CreateBodyDataDto } from './dto/create-body-data.dto';
import { UpdateBodyDataDto } from './dto/update-body-data.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('体脂数据')
@Controller('body-data')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class BodyDataController {
  constructor(private readonly bodyDataService: BodyDataService) {}

  @Post()
  @ApiOperation({ 
    summary: '创建体脂数据记录', 
    description: '添加新的体脂数据记录，支持多种身体指标' 
  })
  @ApiResponse({ status: 201, description: '创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async create(@Request() req, @Body() createBodyDataDto: CreateBodyDataDto) {
    const userId = req.user._id.toString();
    return this.bodyDataService.create(userId, createBodyDataDto);
  }

  @Get()
  @ApiOperation({ 
    summary: '获取体脂数据列表', 
    description: '获取当前用户的体脂数据记录列表，支持分页和排序' 
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码，默认为1' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: '每页数量，默认为20' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: '排序字段，默认为measuredAt' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: '排序方向，默认为desc' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async findAll(
    @Request() req,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query('sortBy', new DefaultValuePipe('measuredAt')) sortBy: string,
    @Query('sortOrder', new DefaultValuePipe('desc')) sortOrder: 'asc' | 'desc',
  ) {
    const userId = req.user._id.toString();
    return this.bodyDataService.findAll(userId, page, limit, sortBy, sortOrder);
  }

  @Get('latest')
  @ApiOperation({ 
    summary: '获取最新体脂数据', 
    description: '获取用户最新的一条体脂数据记录' 
  })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async getLatest(@Request() req) {
    const userId = req.user._id.toString();
    return this.bodyDataService.getLatest(userId);
  }

  @Get('statistics')
  @ApiOperation({ 
    summary: '获取数据统计信息', 
    description: '获取用户体脂数据的统计信息，包括平均值、变化趋势等' 
  })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async getStatistics(@Request() req) {
    const userId = req.user._id.toString();
    return this.bodyDataService.getStatistics(userId);
  }

  @Get('trends/weight')
  @ApiOperation({ 
    summary: '获取体重变化趋势', 
    description: '获取指定天数内的体重变化趋势数据' 
  })
  @ApiQuery({ name: 'days', required: false, type: Number, description: '天数，默认为30天' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async getWeightTrend(
    @Request() req,
    @Query('days', new DefaultValuePipe(30), ParseIntPipe) days: number,
  ) {
    const userId = req.user._id.toString();
    return this.bodyDataService.getWeightTrend(userId, days);
  }

  @Get('trends/body-fat')
  @ApiOperation({ 
    summary: '获取体脂率变化趋势', 
    description: '获取指定天数内的体脂率变化趋势数据' 
  })
  @ApiQuery({ name: 'days', required: false, type: Number, description: '天数，默认为30天' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async getBodyFatTrend(
    @Request() req,
    @Query('days', new DefaultValuePipe(30), ParseIntPipe) days: number,
  ) {
    const userId = req.user._id.toString();
    return this.bodyDataService.getBodyFatTrend(userId, days);
  }

  @Get('date-range')
  @ApiOperation({ 
    summary: '获取日期范围内的数据', 
    description: '获取指定日期范围内的体脂数据记录' 
  })
  @ApiQuery({ name: 'startDate', required: true, type: String, description: '开始日期 (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDate', required: true, type: String, description: '结束日期 (YYYY-MM-DD)' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 400, description: '日期格式错误' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async findByDateRange(
    @Request() req,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    const userId = req.user._id.toString();
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    return this.bodyDataService.findByDateRange(userId, start, end);
  }

  @Get(':id')
  @ApiOperation({ 
    summary: '获取单条体脂数据', 
    description: '根据ID获取特定的体脂数据记录' 
  })
  @ApiParam({ name: 'id', description: '体脂数据记录ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '数据记录不存在' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  @ApiResponse({ status: 403, description: '无权访问此数据' })
  async findOne(@Param('id') id: string, @Request() req) {
    const userId = req.user._id.toString();
    return this.bodyDataService.findOne(id, userId);
  }

  @Patch(':id')
  @ApiOperation({ 
    summary: '更新体脂数据记录', 
    description: '更新指定的体脂数据记录' 
  })
  @ApiParam({ name: 'id', description: '体脂数据记录ID' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '数据记录不存在' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  @ApiResponse({ status: 403, description: '无权修改此数据' })
  async update(
    @Param('id') id: string, 
    @Request() req,
    @Body() updateBodyDataDto: UpdateBodyDataDto
  ) {
    const userId = req.user._id.toString();
    return this.bodyDataService.update(id, userId, updateBodyDataDto);
  }

  @Delete(':id')
  @ApiOperation({ 
    summary: '删除体脂数据记录', 
    description: '删除指定的体脂数据记录' 
  })
  @ApiParam({ name: 'id', description: '体脂数据记录ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '数据记录不存在' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  @ApiResponse({ status: 403, description: '无权删除此数据' })
  async remove(@Param('id') id: string, @Request() req) {
    const userId = req.user._id.toString();
    return this.bodyDataService.remove(id, userId);
  }
}