import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BodyData, BodyDataDocument } from '../schemas/body-data.schema';
import { CreateBodyDataDto } from './dto/create-body-data.dto';
import { UpdateBodyDataDto } from './dto/update-body-data.dto';

@Injectable()
export class BodyDataService {
  constructor(
    @InjectModel(BodyData.name) private bodyDataModel: Model<BodyDataDocument>,
  ) {}

  /**
   * 创建体脂数据记录
   */
  async create(userId: string, createBodyDataDto: CreateBodyDataDto): Promise<BodyData> {
    const newBodyData = new this.bodyDataModel({
      ...createBodyDataDto,
      userId,
      measuredAt: createBodyDataDto.measuredAt ? new Date(createBodyDataDto.measuredAt) : new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return newBodyData.save();
  }

  /**
   * 获取用户所有体脂数据记录
   */
  async findAll(
    userId: string,
    page: number = 1,
    limit: number = 20,
    sortBy: string = 'measuredAt',
    sortOrder: 'asc' | 'desc' = 'desc',
  ): Promise<{ data: BodyData[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;
    const sortObj: Record<string, 1 | -1> = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const [data, total] = await Promise.all([
      this.bodyDataModel
        .find({ userId })
        .sort(sortObj)
        .skip(skip)
        .limit(limit)
        .populate('deviceId', 'name brand model')
        .exec(),
      this.bodyDataModel.countDocuments({ userId }),
    ]);

    return {
      data,
      total,
      page,
      limit,
    };
  }

  /**
   * 获取指定日期范围的体脂数据
   */
  async findByDateRange(
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<BodyData[]> {
    return this.bodyDataModel
      .find({
        userId,
        measuredAt: {
          $gte: startDate,
          $lte: endDate,
        },
      })
      .sort({ measuredAt: -1 })
      .populate('deviceId', 'name brand model')
      .exec();
  }

  /**
   * 获取单条体脂数据记录
   */
  async findOne(id: string, userId: string): Promise<BodyData> {
    const bodyData = await this.bodyDataModel
      .findById(id)
      .populate('deviceId', 'name brand model')
      .exec();

    if (!bodyData) {
      throw new NotFoundException('体脂数据记录不存在');
    }

    if (bodyData.userId.toString() !== userId) {
      throw new ForbiddenException('无权访问此数据');
    }

    return bodyData;
  }

  /**
   * 更新体脂数据记录
   */
  async update(id: string, userId: string, updateBodyDataDto: UpdateBodyDataDto): Promise<BodyData> {
    const bodyData = await this.bodyDataModel.findById(id);

    if (!bodyData) {
      throw new NotFoundException('体脂数据记录不存在');
    }

    if (bodyData.userId.toString() !== userId) {
      throw new ForbiddenException('无权修改此数据');
    }

    const updatedBodyData = await this.bodyDataModel.findByIdAndUpdate(
      id,
      {
        ...updateBodyDataDto,
        updatedAt: new Date(),
      },
      { new: true },
    ).populate('deviceId', 'name brand model');

    return updatedBodyData;
  }

  /**
   * 删除体脂数据记录
   */
  async remove(id: string, userId: string): Promise<{ message: string }> {
    const bodyData = await this.bodyDataModel.findById(id);

    if (!bodyData) {
      throw new NotFoundException('体脂数据记录不存在');
    }

    if (bodyData.userId.toString() !== userId) {
      throw new ForbiddenException('无权删除此数据');
    }

    await this.bodyDataModel.findByIdAndDelete(id);

    return { message: '体脂数据记录删除成功' };
  }

  /**
   * 获取最新的体脂数据记录
   */
  async getLatest(userId: string): Promise<BodyData | null> {
    return this.bodyDataModel
      .findOne({ userId })
      .sort({ measuredAt: -1 })
      .populate('deviceId', 'name brand model')
      .exec();
  }

  /**
   * 获取体重变化趋势数据
   */
  async getWeightTrend(userId: string, days: number = 30): Promise<any[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const trendData = await this.bodyDataModel
      .find({
        userId,
        measuredAt: { $gte: startDate },
      })
      .select('weight measuredAt')
      .sort({ measuredAt: 1 })
      .exec();

    return trendData.map(item => ({
      date: item.timestamp.toISOString().split('T')[0],
      weight: item.weight,
    }));
  }

  /**
   * 获取体脂率变化趋势数据
   */
  async getBodyFatTrend(userId: string, days: number = 30): Promise<any[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const trendData = await this.bodyDataModel
      .find({
        userId,
        measuredAt: { $gte: startDate },
        bodyFat: { $exists: true },
      })
      .select('bodyFat measuredAt')
      .sort({ measuredAt: 1 })
      .exec();

    return trendData.map(item => ({
      date: item.timestamp.toISOString().split('T')[0],
      bodyFat: item.bodyFatPercentage,
    }));
  }

  /**
   * 获取数据统计信息
   */
  async getStatistics(userId: string): Promise<any> {
    const latest = await this.getLatest(userId);
    const totalRecords = await this.bodyDataModel.countDocuments({ userId });

    if (!latest) {
      return {
        totalRecords: 0,
        latestRecord: null,
        avgWeight: null,
        avgBodyFat: null,
        weightChange: null,
        bodyFatChange: null,
      };
    }

    // 计算30天内的平均值
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const aggregateData = await this.bodyDataModel.aggregate([
      {
        $match: {
          userId: latest.userId,
          measuredAt: { $gte: thirtyDaysAgo },
        },
      },
      {
        $group: {
          _id: null,
          avgWeight: { $avg: '$weight' },
          avgBodyFat: { $avg: '$bodyFat' },
          minWeight: { $min: '$weight' },
          maxWeight: { $max: '$weight' },
          minBodyFat: { $min: '$bodyFat' },
          maxBodyFat: { $max: '$bodyFat' },
        },
      },
    ]);

    const stats = aggregateData[0] || {};

    // 计算变化趋势（与7天前对比）
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const sevenDaysData = await this.bodyDataModel
      .findOne({
        userId: latest.userId,
        measuredAt: { $lte: sevenDaysAgo },
      })
      .sort({ measuredAt: -1 })
      .exec();

    let weightChange = null;
    let bodyFatChange = null;

    if (sevenDaysData) {
      weightChange = latest.weight - sevenDaysData.weight;
      if (latest.bodyFatPercentage && sevenDaysData.bodyFatPercentage) {
        bodyFatChange = latest.bodyFatPercentage - sevenDaysData.bodyFatPercentage;
      }
    }

    return {
      totalRecords,
      latestRecord: latest,
      avgWeight: stats.avgWeight ? Number(stats.avgWeight.toFixed(1)) : null,
      avgBodyFat: stats.avgBodyFat ? Number(stats.avgBodyFat.toFixed(1)) : null,
      minWeight: stats.minWeight,
      maxWeight: stats.maxWeight,
      minBodyFat: stats.minBodyFat,
      maxBodyFat: stats.maxBodyFat,
      weightChange: weightChange ? Number(weightChange.toFixed(1)) : null,
      bodyFatChange: bodyFatChange ? Number(bodyFatChange.toFixed(1)) : null,
    };
  }
}