import { PartialType } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CreateBodyDataDto } from './create-body-data.dto';

export class UpdateBodyDataDto extends PartialType(CreateBodyDataDto) {
  @ApiProperty({ 
    description: '更新备注', 
    example: '数据已校正',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '更新备注必须是字符串' })
  updateNotes?: string;
}