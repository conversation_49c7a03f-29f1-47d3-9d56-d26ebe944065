import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsOptional, IsString, IsDateString, Min, Max } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class CreateBodyDataDto {
  @ApiProperty({ 
    description: '体重(kg)', 
    example: 68.5,
    minimum: 20,
    maximum: 300
  })
  @IsNotEmpty({ message: '体重不能为空' })
  @IsNumber({}, { message: '体重必须是数字' })
  @Min(20, { message: '体重不能小于20kg' })
  @Max(300, { message: '体重不能大于300kg' })
  @Type(() => Number)
  weight: number;

  @ApiProperty({ 
    description: '体脂率(%)', 
    example: 18.5,
    minimum: 5,
    maximum: 50,
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '体脂率必须是数字' })
  @Min(5, { message: '体脂率不能小于5%' })
  @Max(50, { message: '体脂率不能大于50%' })
  @Type(() => Number)
  bodyFat?: number;

  @ApiProperty({ 
    description: '肌肉量(kg)', 
    example: 52.3,
    minimum: 10,
    maximum: 100,
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '肌肉量必须是数字' })
  @Min(10, { message: '肌肉量不能小于10kg' })
  @Max(100, { message: '肌肉量不能大于100kg' })
  @Type(() => Number)
  muscleMass?: number;

  @ApiProperty({ 
    description: '内脏脂肪等级', 
    example: 8,
    minimum: 1,
    maximum: 30,
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '内脏脂肪等级必须是数字' })
  @Min(1, { message: '内脏脂肪等级不能小于1' })
  @Max(30, { message: '内脏脂肪等级不能大于30' })
  @Type(() => Number)
  visceralFat?: number;

  @ApiProperty({ 
    description: '水分含量(%)', 
    example: 58.2,
    minimum: 40,
    maximum: 80,
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '水分含量必须是数字' })
  @Min(40, { message: '水分含量不能小于40%' })
  @Max(80, { message: '水分含量不能大于80%' })
  @Type(() => Number)
  waterContent?: number;

  @ApiProperty({ 
    description: '骨量(kg)', 
    example: 3.2,
    minimum: 1,
    maximum: 10,
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '骨量必须是数字' })
  @Min(1, { message: '骨量不能小于1kg' })
  @Max(10, { message: '骨量不能大于10kg' })
  @Type(() => Number)
  boneMass?: number;

  @ApiProperty({ 
    description: '基础代谢率(kcal)', 
    example: 1650,
    minimum: 800,
    maximum: 3000,
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '基础代谢率必须是数字' })
  @Min(800, { message: '基础代谢率不能小于800kcal' })
  @Max(3000, { message: '基础代谢率不能大于3000kcal' })
  @Type(() => Number)
  bmr?: number;

  @ApiProperty({ 
    description: '蛋白质含量(%)', 
    example: 20.5,
    minimum: 10,
    maximum: 30,
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '蛋白质含量必须是数字' })
  @Min(10, { message: '蛋白质含量不能小于10%' })
  @Max(30, { message: '蛋白质含量不能大于30%' })
  @Type(() => Number)
  proteinPercentage?: number;

  @ApiProperty({ 
    description: '设备ID', 
    example: '507f1f77bcf86cd799439011',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '设备ID必须是字符串' })
  deviceId?: string;

  @ApiProperty({ 
    description: '测量时间', 
    example: '2024-01-15T08:30:00Z',
    required: false 
  })
  @IsOptional()
  @IsDateString({}, { message: '测量时间格式不正确' })
  measuredAt?: string;

  @ApiProperty({ 
    description: '备注', 
    example: '早晨空腹测量',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '备注必须是字符串' })
  notes?: string;
}