import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BodyDataController } from './body-data.controller';
import { BodyDataService } from './body-data.service';
import { BodyData, BodyDataSchema } from '../schemas/body-data.schema';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: BodyData.name, schema: BodyDataSchema }]),
  ],
  controllers: [BodyDataController],
  providers: [BodyDataService],
  exports: [BodyDataService],
})
export class BodyDataModule {}