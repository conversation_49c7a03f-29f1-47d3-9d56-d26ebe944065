import { Injectable, UnauthorizedException, ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { User, UserDocument } from '../schemas/user.schema';
import { LoginDto, SocialLoginDto } from './dto/login.dto';
import { RegisterDto, ResetPasswordDto, SendVerificationCodeDto } from './dto/register.dto';
import { VerificationCodeService } from '../common/services/verification-code.service';
import { EmailService } from '../common/services/email.service';
import { SmsService } from '../common/services/sms.service';

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private jwtService: JwtService,
    private verificationCodeService: VerificationCodeService,
    private emailService: EmailService,
    private smsService: SmsService,
    private configService: ConfigService,
  ) {}

  /**
   * 使用验证码注册
   */
  async registerWithCode(dto: { identifier: string; code: string }): Promise<{ user: Partial<User>; access_token: string }> {
    const { identifier, code } = dto;

    // 验证验证码
    const isValidCode = this.verificationCodeService.verify(identifier, code, 'register');
    if (!isValidCode) {
      throw new UnauthorizedException('验证码无效或已过期');
    }

    // 检查用户是否已存在
    const existingUser = await this.userModel.findOne({
      $or: [
        { email: identifier },
        { phone: identifier },
      ],
    });

    if (existingUser) {
      throw new ConflictException('用户已存在');
    }

    // 确定是邮箱还是手机号
    const isEmail = identifier.includes('@');

    // 创建用户（无密码）
    const newUser = new this.userModel({
      nickname: isEmail ? identifier.split('@')[0] : `用户${identifier.slice(-4)}`, // 默认昵称
      email: isEmail ? identifier : undefined,
      phone: isEmail ? undefined : identifier,
      authProvider: isEmail ? 'email' : 'phone',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    const savedUser = await newUser.save();

    // 生成JWT令牌
    const payload = {
      userId: savedUser._id,
      email: savedUser.email,
      phone: savedUser.phone,
      nickname: savedUser.nickname,
    };
    const access_token = this.jwtService.sign(payload);

    // 更新最后登录时间
    await this.userModel.findByIdAndUpdate(savedUser._id, {
      lastLoginAt: new Date(),
    });

    // 构建返回的用户信息，排除敏感字段
    const userResponse: Partial<User> = {
      _id: savedUser._id,
      email: savedUser.email,
      phone: savedUser.phone,
      nickname: savedUser.nickname,
      authProvider: savedUser.authProvider,
      isActive: savedUser.isActive,
      createdAt: savedUser.createdAt,
      updatedAt: savedUser.updatedAt,
      lastLoginAt: savedUser.lastLoginAt,
      preferences: savedUser.preferences,
      profile: savedUser.profile,
    };

    return {
      user: userResponse,
      access_token,
    };
  }

  /**
   * 用户注册
   */
  async register(registerDto: RegisterDto): Promise<{ user: Partial<User>; access_token: string }> {
    const { email, phone, password, nickname, gender, birthDate, height, registerType } = registerDto;

    // 检查用户是否已存在
    const existingUser = await this.userModel.findOne({
      $or: [
        ...(email ? [{ email }] : []),
        ...(phone ? [{ phone }] : []),
      ],
    });

    if (existingUser) {
      throw new ConflictException('用户已存在');
    }

    // 密码加密
    const hashedPassword = await bcrypt.hash(password, 12);

    // 创建用户
    const newUser = new this.userModel({
      nickname,
      email,
      phone,
      password: hashedPassword,
      profile: {
        gender,
        birthDate: birthDate ? new Date(birthDate) : undefined,
        height,
      },
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    const savedUser = await newUser.save();

    // 生成JWT令牌
    const payload = { 
      userId: savedUser._id, 
      email: savedUser.email, 
      phone: savedUser.phone,
      nickname: savedUser.nickname 
    };
    const access_token = this.jwtService.sign(payload);

    // 返回用户信息（不包含密码）
    const userResponse = savedUser.toObject();
    delete userResponse.password;

    return {
      user: userResponse,
      access_token,
    };
  }

  /**
   * 用户登录
   */
  async login(loginDto: LoginDto): Promise<{ user: Partial<User>; access_token: string }> {
    const { email, phone, password, loginType } = loginDto;

    // 根据登录类型查找用户
    const query = loginType === 'phone' && phone ? { phone } : { email };
    const user = await this.userModel.findOne(query).select('+password');

    if (!user) {
      throw new UnauthorizedException('用户不存在');
    }

    if (!user.isActive) {
      throw new UnauthorizedException('账户已被禁用');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('密码错误');
    }

    // 更新最后登录时间
    await this.userModel.findByIdAndUpdate(user._id, { 
      lastLoginAt: new Date(),
      updatedAt: new Date(),
    });

    // 生成JWT令牌
    const payload = { 
      userId: user._id, 
      email: user.email, 
      phone: user.phone,
      nickname: user.nickname 
    };
    const access_token = this.jwtService.sign(payload);

    // 返回用户信息（不包含密码）
    const userResponse = user.toObject();
    delete userResponse.password;

    return {
      user: userResponse,
      access_token,
    };
  }

  /**
   * 社交登录（Google/Apple）
   */
  async socialLogin(socialLoginDto: SocialLoginDto): Promise<{ user: Partial<User>; access_token: string }> {
    const { accessToken, provider } = socialLoginDto;

    let userInfo: any;
    try {
      // 根据不同平台验证令牌并获取用户信息
      if (provider === 'google') {
        userInfo = await this.verifyGoogleToken(accessToken);
      } else if (provider === 'apple') {
        userInfo = await this.verifyAppleToken(accessToken);
      }
    } catch (error) {
      throw new UnauthorizedException('第三方登录验证失败');
    }

    // 查找或创建用户
    let user = await this.userModel.findOne({ 
      $or: [
        { email: userInfo.email },
        { [`socialAccounts.${provider}.id`]: userInfo.id },
      ]
    });

    if (!user) {
      // 创建新用户
      user = new this.userModel({
        nickname: userInfo.name || userInfo.email.split('@')[0],
        email: userInfo.email,
        avatar: userInfo.picture,
        socialAccounts: {
          [provider]: {
            id: userInfo.id,
            email: userInfo.email,
            name: userInfo.name,
          },
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      await user.save();
    } else {
      // 更新社交账户信息和最后登录时间
      user.socialAccounts = user.socialAccounts || {};
      user.socialAccounts[provider] = {
        id: userInfo.id,
        email: userInfo.email,
        name: userInfo.name,
      };
      user.lastLoginAt = new Date();
      user.updatedAt = new Date();
      await user.save();
    }

    // 生成JWT令牌
    const payload = { 
      userId: user._id, 
      email: user.email, 
      phone: user.phone,
      nickname: user.nickname 
    };
    const access_token = this.jwtService.sign(payload);

    // 返回用户信息
    const userResponse = user.toObject();
    delete userResponse.password;

    return {
      user: userResponse,
      access_token,
    };
  }

  /**
   * 在后端使用 Google 授权码与 Google 交换 access_token 并进行登录
   */
  async exchangeGoogleCode(code: string, redirectUri?: string): Promise<{ user: Partial<User>; access_token: string }> {
    try {
      const clientId = this.configService.get('GOOGLE_CLIENT_ID');
      const clientSecret = this.configService.get('GOOGLE_CLIENT_SECRET');
      if (!clientId || !clientSecret) {
        throw new Error('Google OAuth 未在服务器上配置');
      }

      const params = new URLSearchParams();
      params.append('code', code);
      params.append('client_id', clientId);
      params.append('client_secret', clientSecret);
      params.append('grant_type', 'authorization_code');
      if (redirectUri) params.append('redirect_uri', redirectUri);

      const tokenRes = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: params.toString(),
      });

      if (!tokenRes.ok) {
        const text = await tokenRes.text();
        throw new Error(`从Google交换令牌失败: ${text}`);
      }

      const tokenJson = await tokenRes.json();
      const accessToken = tokenJson.access_token;
      if (!accessToken) throw new Error('未从 Google 获取 access_token');

      // 调用已有的社交登录逻辑
      return this.socialLogin({ accessToken, provider: 'google' } as any);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 使用验证码登录（邮箱或手机号）
   */
  async loginWithCode(dto: { identifier: string; code: string }): Promise<{ user: Partial<User>; access_token: string }> {
    const { identifier, code } = dto;

    // 验证验证码
    const isValid = this.verificationCodeService.verify(identifier, code, 'login');
    if (!isValid) {
      throw new UnauthorizedException('验证码无效或已过期');
    }

    // 查找用户
    const user = await this.userModel.findOne({
      $or: [{ email: identifier }, { phone: identifier }],
    });

    if (!user) {
      throw new UnauthorizedException('用户不存在');
    }

    if (!user.isActive) {
      throw new UnauthorizedException('账户已被禁用');
    }

    // 更新最后登录时间
    await this.userModel.findByIdAndUpdate(user._id, {
      lastLoginAt: new Date(),
      updatedAt: new Date(),
    });

    // 生成JWT令牌
    const payload = {
      userId: user._id,
      email: user.email,
      phone: user.phone,
      nickname: user.nickname,
    };
    const access_token = this.jwtService.sign(payload);

    const userResponse = user.toObject();
    delete userResponse.password;

    return {
      user: userResponse,
      access_token,
    };
  }

  /**
   * 验证JWT令牌
   */
  async validateUser(payload: any): Promise<User> {
    const user = await this.userModel.findById(payload.userId);
    if (!user || !user.isActive) {
      throw new UnauthorizedException('用户不存在或已被禁用');
    }
    return user;
  }

  /**
   * 验证访问令牌
   */
  async validateToken(req: any): Promise<{ valid: boolean; user?: Partial<User>; message: string; timestamp: string }> {
    try {
      // 从请求头中提取token
      const authHeader = req.headers?.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new UnauthorizedException('缺少访问令牌');
      }

      const token = authHeader.substring(7); // 移除 'Bearer ' 前缀

      // 验证JWT令牌
      const payload = this.jwtService.verify(token);

      // 根据payload查找用户
      const user = await this.userModel.findById(payload.userId);
      if (!user) {
        throw new UnauthorizedException('用户不存在');
      }

      if (!user.isActive) {
        throw new UnauthorizedException('账户已被禁用');
      }

      // 构建返回的用户信息，排除敏感字段
      const userResponse: Partial<User> = {
        _id: user._id,
        email: user.email,
        phone: user.phone,
        nickname: user.nickname,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastLoginAt: user.lastLoginAt,
        profile: user.profile,
      };

      return {
        valid: true,
        user: userResponse,
        message: '令牌验证成功',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      // 处理JWT验证错误
      if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('令牌格式无效');
      } else if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('令牌已过期');
      } else if (error.name === 'NotBeforeError') {
        throw new UnauthorizedException('令牌尚未生效');
      } else {
        throw error; // 重新抛出其他错误
      }
    }
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(user: User): Promise<{ access_token: string; user: Partial<User> }> {
    // 生成新的访问令牌
    const payload = {
      userId: user._id,
      email: user.email,
      phone: user.phone,
      nickname: user.nickname,
    };
    const access_token = this.jwtService.sign(payload);

    // 更新最后活跃时间
    await this.userModel.findByIdAndUpdate(user._id, {
      lastLoginAt: new Date(),
      updatedAt: new Date(),
    });

    // 构建返回的用户信息，排除敏感字段
    const userResponse: Partial<User> = {
      _id: user._id,
      email: user.email,
      phone: user.phone,
      nickname: user.nickname,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLoginAt: user.lastLoginAt,
      profile: user.profile,
    };

    return {
      access_token,
      user: userResponse,
    };
  }

  /**
   * 从请求中刷新访问令牌
   */
  async refreshTokenFromRequest(req: any): Promise<{ access_token: string; user: Partial<User>; message: string }> {
    try {
      // 从请求头中提取token
      const authHeader = req.headers?.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new UnauthorizedException('缺少访问令牌');
      }

      const token = authHeader.substring(7); // 移除 'Bearer ' 前缀

      // 验证JWT令牌
      const payload = this.jwtService.verify(token);

      // 根据payload查找用户
      const user = await this.userModel.findById(payload.userId);
      if (!user) {
        throw new UnauthorizedException('用户不存在');
      }

      if (!user.isActive) {
        throw new UnauthorizedException('账户已被禁用');
      }

      // 调用原有的refreshToken方法
      const result = await this.refreshToken(user);

      return {
        ...result,
        message: '令牌刷新成功',
      };
    } catch (error) {
      // 处理JWT验证错误
      if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('令牌格式无效');
      } else if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('令牌已过期');
      } else if (error.name === 'NotBeforeError') {
        throw new UnauthorizedException('令牌尚未生效');
      } else {
        throw error; // 重新抛出其他错误
      }
    }
  }

  /**
   * 重置密码
   */
  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{ message: string }> {
    const { identifier, verificationCode, newPassword } = resetPasswordDto;

    // 验证验证码
    const isCodeValid = this.verificationCodeService.verify(identifier, verificationCode, 'reset_password');
    if (!isCodeValid) {
      throw new BadRequestException('验证码无效或已过期');
    }

    // 查找用户
    const user = await this.userModel.findOne({
      $or: [{ email: identifier }, { phone: identifier }],
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 更新密码
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    await this.userModel.findByIdAndUpdate(user._id, {
      password: hashedPassword,
      updatedAt: new Date(),
    });

    return { message: '密码重置成功' };
  }

  /**
   * 发送验证码
   */
  async sendVerificationCode(sendCodeDto: SendVerificationCodeDto): Promise<{ message: string }> {
    const { identifier, type } = sendCodeDto;

    // 检查发送频率限制
    if (!this.verificationCodeService.canSendCode(identifier)) {
      throw new BadRequestException('验证码发送太频繁，请稍后再试');
    }

    // 生成并存储验证码
    const code = this.verificationCodeService.storeCode(identifier, type);

    try {
      if (identifier.includes('@')) {
        // 发送邮件验证码
        await this.emailService.sendVerificationCode(identifier, code, type as any);
      } else {
        // 发送短信验证码
        await this.smsService.sendVerificationCode(identifier, code, type as any);
      }

      return { message: '验证码发送成功' };
    } catch (error) {
      // 发送失败时清理存储的验证码
      this.verificationCodeService.verify(identifier, '', type); // 这会删除验证码
      throw new BadRequestException(`验证码发送失败: ${error.message}`);
    }
  }

  /**
   * 验证Google令牌
   */
  private async verifyGoogleToken(accessToken: string): Promise<any> {
    try {
      // 使用Google API验证令牌并获取用户信息
      const response = await fetch(`https://www.googleapis.com/oauth2/v2/userinfo?access_token=${accessToken}`);
      
      if (!response.ok) {
        throw new Error('Google API响应异常');
      }
      
      const userInfo = await response.json();
      
      // 验证返回的数据格式
      if (!userInfo.id || !userInfo.email) {
        throw new Error('无效的Google用户信息');
      }
      
      return {
        id: userInfo.id,
        email: userInfo.email,
        name: userInfo.name || userInfo.email.split('@')[0],
        picture: userInfo.picture,
        verified_email: userInfo.verified_email,
      };
    } catch (error) {
      throw new Error(`Google令牌验证失败: ${error.message}`);
    }
  }

  /**
   * 验证Apple令牌
   */
  private async verifyAppleToken(accessToken: string): Promise<any> {
    try {
      // Apple Sign In 使用 ID Token 而不是 Access Token
      // 这里应该解析和验证 JWT ID Token
      const jwt = require('jsonwebtoken');
      
      // 在实际项目中，您需要:
      // 1. 从 Apple 获取公钥
      // 2. 验证 JWT 签名
      // 3. 验证 JWT 声明（aud, iss, exp 等）
      
      // 这里使用简化的实现，仅用于演示
      let decoded;
      try {
        // 警告: 生产环境中必须使用Apple的公钥验证
        decoded = jwt.decode(accessToken, { complete: true });
      } catch (error) {
        throw new Error('Apple ID Token 解析失败');
      }
      
      if (!decoded || !decoded.payload) {
        throw new Error('无效的Apple ID Token');
      }
      
      const payload = decoded.payload;
      
      return {
        id: payload.sub, // Apple 用户 ID
        email: payload.email,
        name: payload.name || payload.email?.split('@')[0] || 'Apple用户',
        email_verified: payload.email_verified,
      };
    } catch (error) {
      throw new Error(`Apple令牌验证失败: ${error.message}`);
    }
  }
}