import { Is<PERSON>mail, <PERSON>NotEmpty, <PERSON>S<PERSON>, <PERSON><PERSON><PERSON>th, <PERSON><PERSON>ptional, IsEnum, IsPhoneNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RegisterDto {
  @ApiProperty({ 
    description: '用户昵称', 
    example: '健康达人',
    minLength: 2,
    maxLength: 20
  })
  @IsNotEmpty({ message: '昵称不能为空' })
  @IsString({ message: '昵称必须是字符串' })
  @MinLength(2, { message: '昵称长度至少2位' })
  nickname: string;

  @ApiProperty({ 
    description: '邮箱地址', 
    example: '<EMAIL>',
    required: false 
  })
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  @IsOptional()
  email?: string;

  @ApiProperty({ 
    description: '手机号码', 
    example: '13800138000',
    required: false 
  })
  @IsString({ message: '手机号码必须是字符串' })
  @IsOptional()
  phone?: string;

  @ApiProperty({ 
    description: '密码', 
    example: 'password123',
    minLength: 6 
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码必须是字符串' })
  @MinLength(6, { message: '密码长度至少6位' })
  password: string;

  @ApiProperty({ 
    description: '性别', 
    example: 'male',
    enum: ['male', 'female', 'other'],
    required: false 
  })
  @IsOptional()
  @IsEnum(['male', 'female', 'other'], { message: '性别值无效' })
  gender?: 'male' | 'female' | 'other';

  @ApiProperty({ 
    description: '出生日期', 
    example: '1990-01-01',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '出生日期必须是字符串' })
  birthDate?: string;

  @ApiProperty({ 
    description: '身高(cm)', 
    example: 170,
    required: false 
  })
  @IsOptional()
  height?: number;

  @ApiProperty({ 
    description: '注册类型', 
    example: 'email',
    enum: ['email', 'phone'],
    required: false,
    default: 'email'
  })
  @IsOptional()
  @IsString()
  registerType?: 'email' | 'phone' = 'email';
}

export class ResetPasswordDto {
  @ApiProperty({ 
    description: '邮箱地址或手机号码', 
    example: '<EMAIL>' 
  })
  @IsNotEmpty({ message: '账户标识不能为空' })
  @IsString({ message: '账户标识必须是字符串' })
  identifier: string;

  @ApiProperty({ 
    description: '验证码', 
    example: '123456' 
  })
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码必须是字符串' })
  verificationCode: string;

  @ApiProperty({ 
    description: '新密码', 
    example: 'newpassword123',
    minLength: 6 
  })
  @IsNotEmpty({ message: '新密码不能为空' })
  @IsString({ message: '新密码必须是字符串' })
  @MinLength(6, { message: '新密码长度至少6位' })
  newPassword: string;
}

export class SendVerificationCodeDto {
  @ApiProperty({ 
    description: '邮箱地址或手机号码', 
    example: '<EMAIL>' 
  })
  @IsNotEmpty({ message: '账户标识不能为空' })
  @IsString({ message: '账户标识必须是字符串' })
  identifier: string;

  @ApiProperty({ 
    description: '验证码类型', 
    example: 'reset_password',
    enum: ['register', 'reset_password', 'login'] 
  })
  @IsNotEmpty({ message: '验证码类型不能为空' })
  @IsEnum(['register', 'reset_password', 'login'], { message: '验证码类型无效' })
  type: 'register' | 'reset_password' | 'login';
}