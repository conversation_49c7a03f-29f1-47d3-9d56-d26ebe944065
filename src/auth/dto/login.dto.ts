import { IsEmail, IsNotEmpty, IsS<PERSON>, <PERSON><PERSON><PERSON>th, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({ 
    description: '邮箱地址', 
    example: '<EMAIL>',
    required: false 
  })
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  @IsOptional()
  email?: string;

  @ApiProperty({ 
    description: '手机号码', 
    example: '13800138000',
    required: false 
  })
  @IsString({ message: '手机号码必须是字符串' })
  @IsOptional()
  phone?: string;

  @ApiProperty({ 
    description: '密码', 
    example: 'password123',
    minLength: 6 
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码必须是字符串' })
  @MinLength(6, { message: '密码长度至少6位' })
  password: string;

  @ApiProperty({ 
    description: '登录类型', 
    example: 'email',
    enum: ['email', 'phone'],
    required: false,
    default: 'email'
  })
  @IsOptional()
  @IsString()
  loginType?: 'email' | 'phone' = 'email';
}

export class SocialLoginDto {
  @ApiProperty({ 
    description: '第三方平台访问令牌', 
    example: 'ya29.a0AfH6SMC...' 
  })
  @IsNotEmpty({ message: '访问令牌不能为空' })
  @IsString({ message: '访问令牌必须是字符串' })
  accessToken: string;

  @ApiProperty({ 
    description: '社交登录平台', 
    example: 'google',
    enum: ['google', 'apple'] 
  })
  @IsNotEmpty({ message: '登录平台不能为空' })
  @IsString({ message: '登录平台必须是字符串' })
  provider: 'google' | 'apple';
}

export class LoginWithCodeDto {
  @ApiProperty({ 
    description: '邮箱地址或手机号码', 
    example: '<EMAIL>' 
  })
  @IsNotEmpty({ message: '账户标识不能为空' })
  @IsString({ message: '账户标识必须是字符串' })
  identifier: string;

  @ApiProperty({ 
    description: '验证码', 
    example: '123456' 
  })
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码必须是字符串' })
  code: string;
}

export class ExchangeCodeDto {
  @ApiProperty({ description: 'OAuth 授权码', example: '4/0AX4Xf...' })
  @IsNotEmpty()
  @IsString()
  code: string;

  @ApiProperty({ description: '回调 redirect URI', example: 'healthylife://redirect' })
  @IsOptional()
  @IsString()
  redirectUri?: string;
}