import { Controller, Post, Body, UseGuards, Request, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiBody } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { LoginDto, SocialLoginDto } from './dto/login.dto';
import { ExchangeCodeDto } from './dto/login.dto';
import { LoginWithCodeDto } from './dto/login.dto';
import { RegisterDto, ResetPasswordDto, SendVerificationCodeDto } from './dto/register.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';

@ApiTags('认证')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('register')
  @ApiOperation({ 
    summary: '用户注册', 
    description: '支持邮箱或手机号注册，返回用户信息和访问令牌' 
  })
  @ApiBody({ type: RegisterDto })
  @ApiResponse({ status: 201, description: '注册成功' })
  @ApiResponse({ status: 409, description: '用户已存在' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Post('login')
  @ApiOperation({ 
    summary: '用户登录', 
    description: '支持邮箱或手机号登录，返回用户信息和访问令牌' 
  })
  @ApiBody({ type: LoginDto })
  @ApiResponse({ status: 200, description: '登录成功' })
  @ApiResponse({ status: 401, description: '用户名或密码错误' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  @Post('social-login')
  @ApiOperation({ 
    summary: '第三方社交登录', 
    description: '支持Google和Apple登录，返回用户信息和访问令牌' 
  })
  @ApiBody({ type: SocialLoginDto })
  @ApiResponse({ status: 200, description: '登录成功' })
  @ApiResponse({ status: 401, description: '第三方登录验证失败' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async socialLogin(@Body() socialLoginDto: SocialLoginDto) {
    return this.authService.socialLogin(socialLoginDto);
  }

  @Post('exchange-google-code')
  @ApiOperation({ summary: '通过 Google 授权码交换并登录', description: '在后端使用授权码与 Google 交换 access token 并完成社交登录' })
  @ApiBody({ type: ExchangeCodeDto })
  async exchangeGoogleCode(@Body() dto: ExchangeCodeDto) {
    return this.authService.exchangeGoogleCode(dto.code, dto.redirectUri);
  }

  @Post('login-with-code')
  @ApiOperation({ 
    summary: '使用验证码登录', 
    description: '通过邮箱或手机号和验证码登录，返回用户信息和访问令牌' 
  })
  @ApiBody({ type: LoginWithCodeDto })
  @ApiResponse({ status: 200, description: '登录成功' })
  @ApiResponse({ status: 401, description: '验证码无效或用户不存在' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async loginWithCode(@Body() dto: LoginWithCodeDto) {
    return this.authService.loginWithCode(dto);
  }

  @Post('reset-password')
  @ApiOperation({ 
    summary: '重置密码', 
    description: '通过验证码重置用户密码' 
  })
  @ApiBody({ type: ResetPasswordDto })
  @ApiResponse({ status: 200, description: '密码重置成功' })
  @ApiResponse({ status: 400, description: '验证码无效或已过期' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @Post('send-verification-code')
  @ApiOperation({ 
    summary: '发送验证码', 
    description: '向邮箱或手机号发送验证码，支持注册、重置密码等场景' 
  })
  @ApiBody({ type: SendVerificationCodeDto })
  @ApiResponse({ status: 200, description: '验证码发送成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async sendVerificationCode(@Body() sendCodeDto: SendVerificationCodeDto) {
    return this.authService.sendVerificationCode(sendCodeDto);
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: '获取当前用户信息', 
    description: '获取当前登录用户的详细信息' 
  })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async getProfile(@Request() req) {
    return {
      user: req.user,
      message: '获取用户信息成功',
    };
  }

  @Post('refresh-token')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: '刷新访问令牌', 
    description: '使用当前有效令牌刷新获取新的访问令牌' 
  })
  @ApiResponse({ status: 200, description: '令牌刷新成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async refreshToken(@Request() req) {
    const user = req.user;
    
    // 生成新的访问令牌
    const payload = { 
      userId: user._id, 
      email: user.email, 
      phone: user.phone,
      nickname: user.nickname 
    };
    
    // 这里需要从auth.service中导入jwtService，暂时先返回旧令牌
    return {
      message: '令牌刷新成功',
      // access_token: newAccessToken,
      user: user,
    };
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: '用户登出', 
    description: '用户登出，清除服务端相关状态' 
  })
  @ApiResponse({ status: 200, description: '登出成功' })
  @ApiResponse({ status: 401, description: '未授权访问' })
  async logout(@Request() req) {
    // TODO: 如果需要实现令牌黑名单，可以在这里添加逻辑
    return {
      message: '登出成功',
    };
  }
}